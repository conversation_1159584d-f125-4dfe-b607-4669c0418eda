ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x28 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2820,len:0x56c
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc2c
load:0x403cb700,len:0x2e58
entry 0x403c890c
I (87) octal_psram: vendor id    : 0x0d (AP)
I (87) octal_psram: dev id       : 0x02 (generation 3)
I (87) octal_psram: density      : 0x03 (64 Mbit)
I (89) octal_psram: good-die     : 0x01 (Pass)
I (93) octal_psram: Latency      : 0x01 (Fixed)
I (98) octal_psram: VCC          : 0x01 (3V)
I (102) octal_psram: SRF          : 0x01 (Fast Refresh)
I (107) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (111) octal_psram: BurstLen     : 0x01 (32 Byte)
I (116) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (121) octal_psram: DriveStrength: 0x00 (1/1)
I (126) MSPI Timing: PSRAM timing tuning index: 4
I (130) esp_psram: Found 8MB PSRAM device
I (134) esp_psram: Speed: 80MHz
I (137) cpu_start: Multicore app
I (151) cpu_start: Pro cpu start user code
I (151) cpu_start: cpu freq: 240000000 Hz
I (151) app_init: Application information:
I (151) app_init: Project name:     xiaozhi
I (155) app_init: App version:      1.7.2
I (159) app_init: Compile time:     Jun  8 2025 03:43:48
I (164) app_init: ELF file SHA256:  01bc24758...
I (168) app_init: ESP-IDF:          v5.4.1-dirty
I (173) efuse_init: Min chip rev:     v0.0
I (176) efuse_init: Max chip rev:     v0.99 
I (180) efuse_init: Chip rev:         v0.2
I (184) heap_init: Initializing. RAM available for dynamic allocation:
I (191) heap_init: At 3FCAD868 len 0003BEA8 (239 KiB): RAM
I (196) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (201) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (206) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (213) spi_flash: detected chip: generic
I (216) spi_flash: flash io: qio
I (220) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (226) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (233) main_task: Started on CPU0
I (263) esp_psram: Reserving pool of 48K of internal memory for DMA/internal allocations
I (263) main_task: Calling app_main()
I (263) BackgroundTask: background_task started
I (273) Board: UUID=30f4e277-ee46-4fb2-9fa9-f881be7b7b8d SKU=bread-compact-wifi
I (273) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (283) button: IoT Button Version: 4.1.3
I (283) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (293) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (303) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (313) CompactWifiBoard: Install SSD1306 driver
I (313) CompactWifiBoard: SSD1306 driver installed
I (323) CompactWifiBoard: Turning display on
I (423) Display: Power management not supported
I (423) OledDisplay: Initialize LVGL
I (423) LVGL: Starting LVGL task
I (423) OledDisplay: Adding OLED display
I (443) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (443) MCP: Add tool: self.lamp.get_state
I (443) MCP: Add tool: self.lamp.turn_on
I (453) MCP: Add tool: self.lamp.turn_off
I (453) Application: STATE: starting
I (453) NoAudioCodec: Simplex channels created
I (463) Application: WiFi board detected, setting opus encoder complexity to 0
I (463) AudioCodec: Set input enable to true
I (473) AudioCodec: Set output enable to true
I (473) AudioCodec: Audio codec started
I (483) Application: STATE: configuring
I (483) DnsServer: Starting DNS server
I (483) pp: pp rom version: e7ae62f
I (493) net80211: net80211 rom version: e7ae62f
I (503) wifi:wifi driver task: 3fcd9304, prio:23, stack:6144, core=0
I (503) wifi:wifi firmware version: 79fa3f41ba
I (503) wifi:wifi certification version: v7.0
I (503) wifi:config NVS flash: enabled
I (513) wifi:config nano formatting: enabled
I (513) wifi:Init data frame dynamic rx buffer num: 8
I (523) wifi:Init dynamic rx mgmt buffer num: 5
I (523) wifi:Init management short buffer num: 32
I (523) wifi:Init dynamic tx buffer num: 32
I (533) wifi:Init static tx FG buffer num: 2
I (533) wifi:Init static rx buffer size: 1600
I (543) wifi:Init static rx buffer num: 6
I (543) wifi:Init dynamic rx buffer num: 8
I (553) wifi_init: rx ba win: 6
I (553) wifi_init: accept mbox: 6
I (553) wifi_init: tcpip mbox: 32
I (553) wifi_init: udp mbox: 6
I (563) wifi_init: tcp mbox: 6
I (563) wifi_init: tcp tx win: 5760
I (563) wifi_init: tcp rx win: 5760
I (573) wifi_init: tcp mss: 1440
I (583) wifi:Set ps type: 0, coexist: 0

I (583) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
W (583) phy_init: failed to load RF calibration data (0x1102), falling back to full calibration
I (613) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(2)
I (623) wifi:mode : sta (b4:3a:45:a1:e4:64) + softAP (b4:3a:45:a1:e4:65)
I (623) wifi:enable tsf
I (623) wifi:Total power save buffer number: 16
I (623) wifi:Init max length of beacon: 752/752
I (633) wifi:Init max length of beacon: 752/752
I (633) WifiConfigurationAp: Access Point started with SSID Xiaozhi-E465
I (633) esp_netif_lwip: DHCP server started on interface WIFI_AP_DEF with IP: ***********
I (653) WifiConfigurationAp: Web server started
W (653) Application: Alert 配网模式: 手机连接热点 Xiaozhi-E465，浏览器访问 http://***********

 []
I (683) WifiBoard: Free internal: 108587 minimal internal: 108587
I (693) Application: Resampling audio from 16000 to 24000
I (693) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (10483) SystemInfo: free sram: 111207 minimal sram: 103587
I (10683) WifiBoard: Free internal: 111207 minimal internal: 103587
I (11273) wifi:new:<1,0>, old:<1,1>, ap:<1,0>, sta:<0,0>, prof:1, snd_ch_cfg:0x0
I (11273) wifi:station: ee:20:c6:13:db:10 join, AID=1, bgn, 20
I (11273) WifiConfigurationAp: Station ee:20:c6:13:db:10 joined, AID=1
I (11763) esp_netif_lwip: DHCP server assigned IP to a client, IP is: ***********
I (12053) DnsServer: Sending DNS response to ***********
I (12063) DnsServer: Sending DNS response to ***********
I (14983) wifi:<ba-add>idx:2 (ifx:1, ee:20:c6:13:db:10), tid:0, ssn:70, winSize:64
I (14993) WifiConfigurationAp: SSID: ChinaNet-k5NQ, RSSI: -28, Authmode: 3
I (14993) WifiConfigurationAp: SSID: TaiY-2.4G, RSSI: -39, Authmode: 4
I (15003) WifiConfigurationAp: SSID: ASUS3100_2G, RSSI: -40, Authmode: 3
I (15003) WifiConfigurationAp: SSID: XTW-RD2.4G, RSSI: -41, Authmode: 4
I (15013) WifiConfigurationAp: SSID: ChinaNet-MXSZ9U, RSSI: -74, Authmode: 4
I (15023) WifiConfigurationAp: SSID: HNXT-C101, RSSI: -75, Authmode: 7
I (15023) WifiConfigurationAp: SSID: KDBZ88856783, RSSI: -77, Authmode: 4
I (15033) WifiConfigurationAp: SSID: ChinaNet-fC9R, RSSI: -80, Authmode: 3
I (15033) WifiConfigurationAp: SSID: mifiwi, RSSI: -80, Authmode: 4
I (15043) WifiConfigurationAp: SSID: FAST_2.4G_0259, RSSI: -81, Authmode: 4
I (15053) WifiConfigurationAp: SSID: Audio_01, RSSI: -81, Authmode: 3
I (15053) WifiConfigurationAp: SSID: zsee, RSSI: -82, Authmode: 3
I (15063) WifiConfigurationAp: SSID: ChinaNet-7AHk, RSSI: -83, Authmode: 4
I (15073) WifiConfigurationAp: SSID: HJ123456, RSSI: -83, Authmode: 3
I (15073) WifiConfigurationAp: SSID: TP-LINK_66A0, RSSI: -83, Authmode: 4
I (15083) WifiConfigurationAp: SSID: CPE-0E50, RSSI: -83, Authmode: 3
I (15083) WifiConfigurationAp: SSID: BCTEK, RSSI: -84, Authmode: 3
I (15093) WifiConfigurationAp: SSID: LH01, RSSI: -84, Authmode: 4
I (15103) WifiConfigurationAp: SSID: 管理处, RSSI: -86, Authmode: 3
I (17063) DnsServer: Sending DNS response to ***********
I (17063) DnsServer: Sending DNS response to ***********
I (17713) DnsServer: Sending DNS response to ***********
I (17713) DnsServer: Sending DNS response to ***********
I (17983) DnsServer: Sending DNS response to ***********
I (17983) DnsServer: Sending DNS response to ***********
I (18023) DnsServer: Sending DNS response to ***********
I (18023) DnsServer: Sending DNS response to ***********
I (20483) SystemInfo: free sram: 108699 minimal sram: 89419
I (20563) WifiConfigurationAp: SSID: ChinaNet-k5NQ, RSSI: -32, Authmode: 3
I (20563) WifiConfigurationAp: SSID: ASUS3100_2G, RSSI: -35, Authmode: 3
I (20563) WifiConfigurationAp: SSID: TaiY-2.4G, RSSI: -38, Authmode: 4
I (20573) WifiConfigurationAp: SSID: XTW-RD2.4G, RSSI: -48, Authmode: 4
I (20583) WifiConfigurationAp: SSID: ChinaNet-MXSZ9U, RSSI: -76, Authmode: 4
I (20583) WifiConfigurationAp: SSID: HNXT-C101, RSSI: -76, Authmode: 7
I (20593) WifiConfigurationAp: SSID: JH66, RSSI: -78, Authmode: 4
I (20603) WifiConfigurationAp: SSID: ChinaNet-fC9R, RSSI: -79, Authmode: 3
I (20603) WifiConfigurationAp: SSID: KDBZ88856783, RSSI: -79, Authmode: 4
I (20613) WifiConfigurationAp: SSID: FAST_2.4G_0259, RSSI: -81, Authmode: 4
I (20623) WifiConfigurationAp: SSID: CPE-0E50, RSSI: -82, Authmode: 3
I (20623) WifiConfigurationAp: SSID: ChinaNet-7AHk, RSSI: -83, Authmode: 4
I (20633) WifiConfigurationAp: SSID: 管理处, RSSI: -85, Authmode: 3
I (20683) WifiBoard: Free internal: 108679 minimal internal: 89419
W (24663) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (24673) WifiConfigurationAp: Connecting to WiFi ASUS3100_2G
I (27853) wifi:primary chan differ, old=1, new=11, start CSA timer
I (28253) wifi:switch to channel 11
I (28253) wifi:ap channel adjust o:1,0 n:11,0
I (28253) wifi:new:<11,0>, old:<1,0>, ap:<11,0>, sta:<0,0>, prof:1, snd_ch_cfg:0x0
I (28263) wifi:new:<11,0>, old:<11,0>, ap:<11,0>, sta:<11,0>, prof:1, snd_ch_cfg:0x0
I (28263) wifi:state: init -> auth (0xb0)
I (28273) wifi:state: auth -> assoc (0x0)
I (28283) wifi:state: assoc -> run (0x10)
I (28283) wifi:<ba-add>idx:0 (ifx:0, 4c:ed:fb:ac:1f:50), tid:0, ssn:0, winSize:64
I (28303) wifi:connected with ASUS3100_2G, aid = 1, channel 11, BW20, bssid = 4c:ed:fb:ac:1f:50
I (28303) wifi:security: WPA2-PSK, phy: bgn, rssi: -36
I (28313) wifi:pm start, type: 0

I (28313) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (28313) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (28323) WifiConfigurationAp: Connected to WiFi ASUS3100_2G
I (28333) wifi:state: run -> init (0x0)
I (28343) wifi:pm stop, total sleep time: lu us / lu us

I (28343) wifi:<ba-del>idx:0, tid:0
I (28343) wifi:new:<11,0>, old:<11,0>, ap:<11,0>, sta:<11,0>, prof:1, snd_ch_cfg:0x0
I (28353) WifiConfigurationAp: Save SSID ASUS3100_2G 11
I (30483) SystemInfo: free sram: 107995 minimal sram: 89419
I (30683) WifiBoard: Free internal: 107995 minimal internal: 89419
I (31523) WifiConfigurationAp: Rebooting...
I (31923) wifi:station: ee:20:c6:13:db:10 leave, AID = 1, reason = 2, bss_flags is 33721443, bss:0x3fce0d10
I (31923) wifi:new:<11,0>, old:<11,0>, ap:<11,0>, sta:<11,0>, prof:1, snd_ch_cfg:0x0
I (31923) wifi:<ba-del>idx:2, tid:0
I (31933) WifiConfigurationAp: Station ee:20:c6:13:db:10 left, AID=1
I (32023) wifi:flush txq
I (32023) wifi:stop sw txq
I (32023) wifi:lmac stop hw txq
SP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x28 (SPI_FAST_FLASH_BOOT)
Saved PC:0x40379ea5
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2820,len:0x56c
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc2c
load:0x403cb700,len:0x2e58
entry 0x403c890c
I (40) octal_psram: vendor id    : 0x0d (AP)
I (41) octal_psram: dev id       : 0x02 (generation 3)
I (41) octal_psram: density      : 0x03 (64 Mbit)
I (43) octal_psram: good-die     : 0x01 (Pass)
I (47) octal_psram: Latency      : 0x01 (Fixed)
I (51) octal_psram: VCC          : 0x01 (3V)
I (55) octal_psram: SRF          : 0x01 (Fast Refresh)
I (60) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (65) octal_psram: BurstLen     : 0x01 (32 Byte)
I (69) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (74) octal_psram: DriveStrength: 0x00 (1/1)
I (79) MSPI Timing: PSRAM timing tuning index: 4
I (83) esp_psram: Found 8MB PSRAM device
I (86) esp_psram: Speed: 80MHz
I (89) cpu_start: Multicore app
I (104) cpu_start: Pro cpu start user code
I (104) cpu_start: cpu freq: 240000000 Hz
I (104) app_init: Application information:
I (104) app_init: Project name:     xiaozhi
I (108) app_init: App version:      1.7.2
I (111) app_init: Compile time:     Jun  8 2025 03:43:48
I (116) app_init: ELF file SHA256:  01bc24758...
I (121) app_init: ESP-IDF:          v5.4.1-dirty
I (125) efuse_init: Min chip rev:     v0.0
I (129) efuse_init: Max chip rev:     v0.99 
I (133) efuse_init: Chip rev:         v0.2
I (137) heap_init: Initializing. RAM available for dynamic allocation:
I (143) heap_init: At 3FCAD868 len 0003BEA8 (239 KiB): RAM
I (148) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (153) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (159) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (166) spi_flash: detected chip: generic
I (169) spi_flash: flash io: qio
I (173) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (178) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (185) main_task: Started on CPU0
I (215) esp_psram: Reserving pool of 48K of internal memory for DMA/internal allocations
I (215) main_task: Calling app_main()
I (235) BackgroundTask: background_task started
I (235) Board: UUID=30f4e277-ee46-4fb2-9fa9-f881be7b7b8d SKU=bread-compact-wifi
I (235) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (245) button: IoT Button Version: 4.1.3
I (245) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (255) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (265) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (275) CompactWifiBoard: Install SSD1306 driver
I (275) CompactWifiBoard: SSD1306 driver installed
I (285) CompactWifiBoard: Turning display on
I (385) Display: Power management not supported
I (385) OledDisplay: Initialize LVGL
I (385) LVGL: Starting LVGL task
I (385) OledDisplay: Adding OLED display
I (405) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (405) MCP: Add tool: self.lamp.get_state
I (405) MCP: Add tool: self.lamp.turn_on
I (415) MCP: Add tool: self.lamp.turn_off
I (415) Application: STATE: starting
I (415) NoAudioCodec: Simplex channels created
I (425) Application: WiFi board detected, setting opus encoder complexity to 0
I (425) AudioCodec: Set input enable to true
I (435) AudioCodec: Set output enable to true
I (435) AudioCodec: Audio codec started
I (445) pp: pp rom version: e7ae62f
I (445) net80211: net80211 rom version: e7ae62f
I (455) wifi:wifi driver task: 3fcd8124, prio:23, stack:6144, core=0
I (455) wifi:wifi firmware version: 79fa3f41ba
I (455) wifi:wifi certification version: v7.0
I (465) wifi:config NVS flash: disabled
I (465) wifi:config nano formatting: enabled
I (465) wifi:Init data frame dynamic rx buffer num: 8
I (475) wifi:Init dynamic rx mgmt buffer num: 5
I (475) wifi:Init management short buffer num: 32
I (485) wifi:Init dynamic tx buffer num: 32
I (485) wifi:Init static tx FG buffer num: 2
I (495) wifi:Init static rx buffer size: 1600
I (495) wifi:Init static rx buffer num: 6
I (495) wifi:Init dynamic rx buffer num: 8
I (505) wifi_init: rx ba win: 6
I (505) wifi_init: accept mbox: 6
I (505) wifi_init: tcpip mbox: 32
I (515) wifi_init: udp mbox: 6
I (515) wifi_init: tcp mbox: 6
I (515) wifi_init: tcp tx win: 5760
I (525) wifi_init: tcp rx win: 5760
I (525) wifi_init: tcp mss: 1440
I (525) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
I (565) wifi:mode : sta (b4:3a:45:a1:e4:64)
I (565) wifi:enable tsf
I (2975) wifi: Found AP: ASUS3100_2G, BSSID: 4c:ed:fb:ac:1f:50, RSSI: -42, Channel: 11, Authmode: 3
W (2975) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (4525) wifi:new:<11,0>, old:<1,0>, ap:<255,255>, sta:<11,0>, prof:1, snd_ch_cfg:0x0
I (4525) wifi:state: init -> auth (0xb0)
I (4525) wifi:state: auth -> assoc (0x0)
I (4525) wifi:state: assoc -> run (0x10)
I (4535) wifi:<ba-add>idx:0 (ifx:0, 4c:ed:fb:ac:1f:50), tid:0, ssn:0, winSize:64
I (4545) wifi:connected with ASUS3100_2G, aid = 1, channel 11, BW20, bssid = 4c:ed:fb:ac:1f:50
I (4545) wifi:security: WPA2-PSK, phy: bgn, rssi: -42
I (4545) wifi:pm start, type: 1

I (4555) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (4565) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (4625) wifi:AP's beacon interval = 102400 us, DTIM period = 3
I (5585) wifi: Got IP: ************
I (5585) esp_netif_handlers: sta ip: ************, mask: *************, gw: ***********
I (5595) Application: STATE: activating
I (5595) Ota: Current version: 1.7.2
I (5605) EspHttp: Opening HTTP connection to https://api.tenclass.net/xiaozhi/ota/
I (5735) esp-x509-crt-bundle: Certificate validated
I (6795) Ota: Current is the latest version
I (6795) Ota: Running partition: ota_0
I (6795) MCP: Add tool: self.get_device_status
I (6795) MCP: Add tool: self.audio_speaker.set_volume
I (6805) MQTT: Connecting to endpoint mqtt.xiaozhi.me
I (6895) esp-x509-crt-bundle: Certificate validated
I (7415) MQTT: Connected to endpoint
I (7415) MODEL_LOADER: The storage free size is 21952 KB
I (7415) MODEL_LOADER: The partition size is 960 KB
I (7425) MODEL_LOADER: Successfully load srmodels
I (7425) AFE: AFE Version: (1MIC_V250121)
I (7425) AFE: Input PCM Config: total 1 channels(1 microphone, 0 playback), sample rate:16000
I (7435) AFE: AFE Pipeline: [input] -> |VAD(WebRTC)| -> [output]
I (7445) AfeAudioProcessor: Audio communication task started, feed size: 512 fetch size: 512
I (7455) AfeWakeWord: Model 0: wn9_nihaoxiaozhi_tts
I (7455) AFE_CONFIG: Set WakeNet Model: wn9_nihaoxiaozhi_tts
MC Quantized wakenet9: wakenet9l_tts1h8_你好小智_3_0.631_0.635, tigger:v4, mode:0, p:0, (May 16 2025 16:07:41)
I (7495) AFE: AFE Version: (1MIC_V250121)
I (7495) AFE: Input PCM Config: total 1 channels(1 microphone, 0 playback), sample rate:16000
I (7505) AFE: AFE Pipeline: [input] -> |VAD(WebRTC)| -> |WakeNet(wn9_nihaoxiaozhi_tts,)| ->  -> [output]
I (7515) AfeWakeWord: Audio detection task started, feed size: 512 fetch size: 512
I (7525) Application: STATE: idle
I (7555) SystemInfo: free sram: 79339 minimal sram: 79339
I (7555) Application: Resampling audio from 16000 to 24000
I (7555) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (10915) Application: STATE: connecting
I (10955) MQTT: Session ID: c10580d1
I (10965) wifi:Set ps type: 0, coexist: 0

I (10965) Application: Wake word detected: 你好小智
I (11315) AfeWakeWord: Encode wake word opus 66 packets in 402 ms
I (11325) Application: STATE: listening
I (11375) Application: >> Hi, 小智
I (11375) Application: STATE: speaking
I (11775) Application: << 嘿，是你啊，咋了？
I (14135) Application: STATE: listening

Error reading data on serial port:
  ¾Ü¾ø·ÃÎÊ¡
