ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x29 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2820,len:0x56c
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc2c
load:0x403cb700,len:0x2e58
entry 0x403c890c
I (40) octal_psram: vendor id    : 0x0d (AP)
I (40) octal_psram: dev id       : 0x02 (generation 3)
I (40) octal_psram: density      : 0x03 (64 Mbit)
I (42) octal_psram: good-die     : 0x01 (Pass)
I (46) octal_psram: Latency      : 0x01 (Fixed)
I (51) octal_psram: VCC          : 0x01 (3V)
I (55) octal_psram: SRF          : 0x01 (Fast Refresh)
I (59) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (64) octal_psram: BurstLen     : 0x01 (32 Byte)
I (69) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (74) octal_psram: DriveStrength: 0x00 (1/1)
I (79) MSPI Timing: PSRAM timing tuning index: 5
I (82) esp_psram: Found 8MB PSRAM device
I (86) esp_psram: Speed: 80MHz
I (89) cpu_start: Multicore app
I (103) cpu_start: Pro cpu start user code
I (103) cpu_start: cpu freq: 240000000 Hz
I (103) app_init: Application information:
I (103) app_init: Project name:     xiaozhi
I (107) app_init: App version:      1.7.5
I (111) app_init: Compile time:     Jun 26 2025 19:35:56
I (116) app_init: ELF file SHA256:  7b4f51a93...
I (120) app_init: ESP-IDF:          v5.4.1-dirty
I (125) efuse_init: Min chip rev:     v0.0
I (129) efuse_init: Max chip rev:     v0.99 
I (133) efuse_init: Chip rev:         v0.2
I (136) heap_init: Initializing. RAM available for dynamic allocation:
I (143) heap_init: At 3FCAD5A0 len 0003C170 (240 KiB): RAM
I (148) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (153) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (158) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (165) spi_flash: detected chip: generic
I (169) spi_flash: flash io: qio
I (172) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (178) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (185) main_task: Started on CPU0
I (215) esp_psram: Reserving pool of 48K of internal memory for DMA/internal allocations
I (215) main_task: Calling app_main()
I (215) BackgroundTask: background_task started
I (225) Board: UUID=00899241-4e18-45a5-9e24-cceed6ceda9f SKU=winxtec-compact-wifi
I (225) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (235) button: IoT Button Version: 4.1.3
I (235) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (245) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (255) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (265) WinxtecCompactWifiBoard: Install SSD1306 driver
I (265) WinxtecCompactWifiBoard: SSD1306 driver installed
I (275) WinxtecCompactWifiBoard: Turning display on
I (375) Display: Power management not supported
I (375) OledDisplay: Initialize LVGL
I (375) LVGL: Starting LVGL task
I (375) OledDisplay: Adding OLED display
I (395) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (395) MCP: Add tool: self.lamp.get_state
I (395) MCP: Add tool: self.lamp.turn_on
I (405) MCP: Add tool: self.lamp.turn_off
I (405) Application: STATE: starting
I (405) Ns4168OutputCodec: NS4168OutputCodec constructor started
I (415) Ns4168OutputCodec: parameters: output_rate=16000
I (415) Ns4168OutputCodec: GPIO: BCLK=15, WS=16, DOUT=7, CTRL=13
I (425) Ns4168OutputCodec: configuring control pin GPIO13
I (425) gpio: GPIO[13]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (435) Ns4168OutputCodec: GPIO configuration succeeded
I (445) Ns4168OutputCodec: control pin initialized to low level (muted state) to prevent startup noise
I (455) Ns4168OutputCodec: delaying I2S channel creation to avoid startup noise
I (455) Ns4168OutputCodec: NS4168OutputCodec constructor completed
I (465) Mems3526AudioCodec: MEMS3526 Microphone constructor started (PDM mode)
I (475) Mems3526AudioCodec: Parameters: sample_rate=16000, clk_pin=5, data_pin=6
I (475) Mems3526AudioCodec: Configuring GPIO pins
I (485) Mems3526AudioCodec: Initializing PDM mode
I (485) Mems3526AudioCodec: PDM configuration: CLK=GPIO5, DATA=GPIO6, sample_rate=16000
I (495) Mems3526AudioCodec: Creating I2S channel with 6 descriptors, 240 frames each
I (505) Mems3526AudioCodec: I2S RX channel created successfully, handle: 0x3c291078
I (505) Mems3526AudioCodec: Initializing PDM RX mode...
I (515) Mems3526AudioCodec: GPIO configuration completed
I (515) Mems3526AudioCodec: PDM mode initialization completed successfully
I (525) Mems3526AudioCodec: MEMS3526 PDM input channel created on I2S0
I (535) Mems3526AudioCodec: MEMS3526 Microphone constructor completed (PDM mode)
I (535) CompositeAudioCodec: CompositeAudioCodec constructor started
I (545) CompositeAudioCodec: Output codec: 0x3fca7b98, Input codec: 0x3fca7b68, Take ownership: false
I (555) CompositeAudioCodec: CompositeAudioCodec constructor completed
I (565) Application: WiFi board detected, setting opus encoder complexity to 0
I (565) CompositeAudioCodec: CompositeAudioCodec starting...
I (575) CompositeAudioCodec: Starting output codec...
I (575) Ns4168OutputCodec: Volume loaded from settings: 70 (default was 70)
I (585) Ns4168OutputCodec: executing delayed I2S initialization
I (595) Ns4168OutputCodec: NS4168 I2S output channel created on I2S1 (mono mode)
I (595) Ns4168OutputCodec: delayed I2S initialization completed
I (605) Ns4168OutputCodec: I2S channel enabled, waiting for stabilization...
I (655) Ns4168OutputCodec: NS4168 amplifier enabled
I (675) AudioCodec: Set output enable to true
I (675) Ns4168OutputCodec: Waiting for audio system to be fully ready...
I (875) Ns4168OutputCodec: Audio system ready: tx_handle=0x3c29bca0, output_enabled=true
I (875) Ns4168OutputCodec: NS4168OutputCodec started successfully and ready for audio output
I (875) CompositeAudioCodec: Starting input codec...
I (875) Mems3526AudioCodec: Starting MEMS3526 Microphone...
I (885) Mems3526AudioCodec: Enabling I2S RX channel...
I (885) Mems3526AudioCodec: I2S RX channel enabled successfully
I (895) Mems3526AudioCodec: MEMS3526 input enabled
I (895) Mems3526AudioCodec: Input enabled status: true
I (905) Mems3526AudioCodec: RX handle: 0x3c291078
I (1005) Mems3526AudioCodec: Read 160 samples from MEMS3526 (requested 160), amplitude range: 0 to 1100
I (1005) Mems3526AudioCodec: Test read result: 160 samples
I (1005) Mems3526AudioCodec: MEMS3526 Microphone started successfully
I (1005) CompositeAudioCodec: Enabling CompositeAudioCodec input...
I (1015) CompositeAudioCodec: CompositeAudioCodec EnableInput: enabled
I (1025) Mems3526AudioCodec: MEMS3526 input enabled
I (1025) CompositeAudioCodec: Enabling CompositeAudioCodec output...
I (1035) CompositeAudioCodec: CompositeAudioCodec EnableOutput: enabled
I (1035) CompositeAudioCodec: === CompositeAudioCodec Status ===
I (1045) CompositeAudioCodec: Output Codec: 0x3fca7b98 (ownership: false)
I (1055) CompositeAudioCodec: Input Codec: 0x3fca7b68 (ownership: false)
I (1055) CompositeAudioCodec: Output: enabled=true, sample_rate=16000, channels=1
I (1065) CompositeAudioCodec: Input: enabled=true, sample_rate=16000, channels=1, reference=false
I (1075) CompositeAudioCodec: Volume: 70
I (1075) CompositeAudioCodec: ================================
I (1085) CompositeAudioCodec: CompositeAudioCodec started successfully
I (1085) Application: STATE: configuring
I (1095) DnsServer: Starting DNS server
I (1095) pp: pp rom version: e7ae62f
I (1095) net80211: net80211 rom version: e7ae62f
I (1115) wifi:wifi driver task: 3fcd79b4, prio:23, stack:6144, core=0
I (1115) wifi:wifi firmware version: 79fa3f41ba
I (1115) wifi:wifi certification version: v7.0
I (1115) wifi:config NVS flash: enabled
I (1125) wifi:config nano formatting: enabled
I (1125) wifi:Init data frame dynamic rx buffer num: 8
I (1135) wifi:Init dynamic rx mgmt buffer num: 5
I (1135) wifi:Init management short buffer num: 32
I (1135) wifi:Init dynamic tx buffer num: 32
I (1145) wifi:Init static tx FG buffer num: 2
I (1145) wifi:Init static rx buffer size: 1600
I (1155) wifi:Init static rx buffer num: 6
I (1155) wifi:Init dynamic rx buffer num: 8
I (1155) wifi_init: rx ba win: 6
I (1165) wifi_init: accept mbox: 6
I (1165) wifi_init: tcpip mbox: 32
I (1165) wifi_init: udp mbox: 6
I (1175) wifi_init: tcp mbox: 6
I (1175) wifi_init: tcp tx win: 5760
I (1175) wifi_init: tcp rx win: 5760
I (1185) wifi_init: tcp mss: 1440
I (1195) wifi:Set ps type: 0, coexist: 0

I (1195) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
W (1195) phy_init: failed to load RF calibration data (0x1102), falling back to full calibration
I (1225) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(2)
I (1235) wifi:mode : sta (28:37:2f:89:7c:24) + softAP (28:37:2f:89:7c:25)
I (1235) wifi:enable tsf
I (1235) wifi:Total power save buffer number: 16
I (1235) wifi:Init max length of beacon: 752/752
I (1245) wifi:Init max length of beacon: 752/752
I (1245) WifiConfigurationAp: Access Point started with SSID Xiaozhi-7C25
I (1245) esp_netif_lwip: DHCP server started on interface WIFI_AP_DEF with IP: ***********
I (1265) WifiConfigurationAp: Web server started
W (1265) Application: Alert 配网模式: 手机连接热点 Xiaozhi-7C25，浏览器访问 http://***********

 []
I (1295) Application: ResetDecoder called - current state: configuring
I (1295) Application: ResetDecoder: Enabled output (state: configuring)
I (1295) WifiBoard: Free internal: 115171 minimal internal: 115171
I (11095) SystemInfo: free sram: 118755 minimal sram: 110863
I (11305) WifiBoard: Free internal: 118755 minimal internal: 110863
I (12125) wifi:new:<1,0>, old:<1,1>, ap:<1,0>, sta:<0,0>, prof:1, snd_ch_cfg:0x0
I (12125) wifi:station: 1e:13:3b:8a:f3:62 join, AID=1, bgn, 20
I (12125) WifiConfigurationAp: Station 1e:13:3b:8a:f3:62 joined, AID=1
I (12265) wifi:<ba-add>idx:2 (ifx:1, 1e:13:3b:8a:f3:62), tid:0, ssn:0, winSize:64
I (12275) esp_netif_lwip: DHCP server assigned IP to a client, IP is: ***********
I (12685) DnsServer: Sending DNS response to ***********
I (12685) DnsServer: Sending DNS response to ***********
I (16535) WifiConfigurationAp: SSID: ChinaNet-k5NQ, RSSI: -49, Authmode: 3
I (16535) WifiConfigurationAp: SSID: ASUS3100_2G, RSSI: -49, Authmode: 3
I (16535) WifiConfigurationAp: SSID: XTW-RD2.4G, RSSI: -60, Authmode: 4
I (16545) WifiConfigurationAp: SSID: TaiY-2.4G, RSSI: -62, Authmode: 4
I (16545) WifiConfigurationAp: SSID: HNXT-C101, RSSI: -86, Authmode: 7
I (16555) WifiConfigurationAp: SSID: ChinaNet-MXSZ9U, RSSI: -93, Authmode: 4
I (16565) WifiConfigurationAp: SSID: LH01, RSSI: -93, Authmode: 4
I (16565) WifiConfigurationAp: SSID: FAST_2.4G_0259, RSSI: -94, Authmode: 4
I (17475) DnsServer: Sending DNS response to ***********
I (17475) DnsServer: Sending DNS response to ***********
I (21085) SystemInfo: free sram: 116323 minimal sram: 87683
I (21305) WifiBoard: Free internal: 115891 minimal internal: 87683
I (22045) WifiConfigurationAp: SSID: ChinaNet-k5NQ, RSSI: -52, Authmode: 3
I (22045) WifiConfigurationAp: SSID: ASUS3100_2G, RSSI: -54, Authmode: 3
I (22045) WifiConfigurationAp: SSID: XTW-RD2.4G, RSSI: -59, Authmode: 4
I (22055) WifiConfigurationAp: SSID: TaiY-2.4G, RSSI: -62, Authmode: 4
I (22065) WifiConfigurationAp: SSID: HNXT-C101, RSSI: -89, Authmode: 7
I (22065) WifiConfigurationAp: SSID: LH01, RSSI: -91, Authmode: 4
I (22075) WifiConfigurationAp: SSID: ChinaNet-MXSZ9U, RSSI: -93, Authmode: 4
I (22085) WifiConfigurationAp: SSID: FAST_2.4G_0259, RSSI: -93, Authmode: 4
I (22085) WifiConfigurationAp: SSID: mifiwi, RSSI: -94, Authmode: 4
I (27375) WifiConfigurationAp: SSID: ChinaNet-k5NQ, RSSI: -52, Authmode: 3
I (27385) WifiConfigurationAp: SSID: ASUS3100_2G, RSSI: -54, Authmode: 3
I (27385) WifiConfigurationAp: SSID: XTW-RD2.4G, RSSI: -59, Authmode: 4
I (27385) WifiConfigurationAp: SSID: TaiY-2.4G, RSSI: -62, Authmode: 4
I (27395) WifiConfigurationAp: SSID: HNXT-C101, RSSI: -89, Authmode: 7
I (27405) WifiConfigurationAp: SSID: LH01, RSSI: -91, Authmode: 4
I (27405) WifiConfigurationAp: SSID: ChinaNet-MXSZ9U, RSSI: -93, Authmode: 4
I (27415) WifiConfigurationAp: SSID: FAST_2.4G_0259, RSSI: -93, Authmode: 4
I (27425) WifiConfigurationAp: SSID: mifiwi, RSSI: -94, Authmode: 4
W (28595) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (28605) WifiConfigurationAp: Connecting to WiFi ASUS3100_2G
I (31085) SystemInfo: free sram: 115235 minimal sram: 87683
I (31305) WifiBoard: Free internal: 115155 minimal internal: 87683
I (31785) wifi:primary chan differ, old=1, new=11, start CSA timer
I (32185) wifi:switch to channel 11
I (32185) wifi:ap channel adjust o:1,0 n:11,0
I (32185) wifi:new:<11,0>, old:<1,0>, ap:<11,0>, sta:<0,0>, prof:1, snd_ch_cfg:0x0
I (32185) wifi:new:<11,0>, old:<11,0>, ap:<11,0>, sta:<11,0>, prof:1, snd_ch_cfg:0x0
I (32195) wifi:state: init -> auth (0xb0)
I (32225) wifi:state: auth -> assoc (0x0)
I (32225) wifi:state: assoc -> run (0x10)
I (32235) wifi:<ba-add>idx:0 (ifx:0, 4c:ed:fb:ac:1f:50), tid:0, ssn:0, winSize:64
I (32245) wifi:connected with ASUS3100_2G, aid = 1, channel 11, BW20, bssid = 4c:ed:fb:ac:1f:50
I (32245) wifi:security: WPA2-PSK, phy: bgn, rssi: -49
I (32255) wifi:pm start, type: 0

I (32255) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (32255) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (32265) wifi:AP's beacon interval = 102400 us, DTIM period = 3
I (32275) WifiConfigurationAp: Connected to WiFi ASUS3100_2G
I (32275) wifi:state: run -> init (0x0)
I (32295) wifi:pm stop, total sleep time: lu us / lu us

I (32295) wifi:<ba-del>idx:0, tid:0
I (32295) wifi:new:<11,0>, old:<11,0>, ap:<11,0>, sta:<11,0>, prof:1, snd_ch_cfg:0x0
I (32295) WifiConfigurationAp: Save SSID ASUS3100_2G 11
I (35505) WifiConfigurationAp: Rebooting...
I (35905) wifi:station: 1e:13:3b:8a:f3:62 leave, AID = 1, reason = 2, bss_flags is 33721443, bss:0x3fcdf6f8
I (35905) wifi:new:<11,0>, old:<11,0>, ap:<11,0>, sta:<11,0>, prof:1, snd_ch_cfg:0x0
I (35905) wifi:<ba-del>idx:2, tid:0
I (35915) WifiConfigurationAp: Station 1e:13:3b:8a:f3:62 left, AID=1
I (35955) wifi:flush txq
I (35955) wifi:stop sw txq
I (35955) wifi:lmac stop hw txq
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x29 (SPI_FAST_FLASH_BOOT)
Saved PC:0x40380fba
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2820,len:0x56c
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc2c
load:0x403cb700,len:0x2e58
entry 0x403c890c
I (40) octal_psram: vendor id    : 0x0d (AP)
I (41) octal_psram: dev id       : 0x02 (generation 3)
I (41) octal_psram: density      : 0x03 (64 Mbit)
I (42) octal_psram: good-die     : 0x01 (Pass)
I (47) octal_psram: Latency      : 0x01 (Fixed)
I (51) octal_psram: VCC          : 0x01 (3V)
I (55) octal_psram: SRF          : 0x01 (Fast Refresh)
I (60) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (65) octal_psram: BurstLen     : 0x01 (32 Byte)
I (69) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (74) octal_psram: DriveStrength: 0x00 (1/1)
I (79) MSPI Timing: PSRAM timing tuning index: 5
I (83) esp_psram: Found 8MB PSRAM device
I (86) esp_psram: Speed: 80MHz
I (89) cpu_start: Multicore app
I (104) cpu_start: Pro cpu start user code
I (104) cpu_start: cpu freq: 240000000 Hz
I (104) app_init: Application information:
I (104) app_init: Project name:     xiaozhi
I (108) app_init: App version:      1.7.5
I (111) app_init: Compile time:     Jun 26 2025 19:35:56
I (116) app_init: ELF file SHA256:  7b4f51a93...
I (121) app_init: ESP-IDF:          v5.4.1-dirty
I (125) efuse_init: Min chip rev:     v0.0
I (129) efuse_init: Max chip rev:     v0.99 
I (133) efuse_init: Chip rev:         v0.2
I (137) heap_init: Initializing. RAM available for dynamic allocation:
I (143) heap_init: At 3FCAD5A0 len 0003C170 (240 KiB): RAM
I (148) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (153) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (159) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (166) spi_flash: detected chip: generic
I (169) spi_flash: flash io: qio
I (173) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (178) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (185) main_task: Started on CPU0
I (215) esp_psram: Reserving pool of 48K of internal memory for DMA/internal allocations
I (215) main_task: Calling app_main()
I (235) BackgroundTask: background_task started
I (235) Board: UUID=00899241-4e18-45a5-9e24-cceed6ceda9f SKU=winxtec-compact-wifi
I (235) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (245) button: IoT Button Version: 4.1.3
I (245) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (255) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (265) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (275) WinxtecCompactWifiBoard: Install SSD1306 driver
I (275) WinxtecCompactWifiBoard: SSD1306 driver installed
I (285) WinxtecCompactWifiBoard: Turning display on
I (385) Display: Power management not supported
I (385) OledDisplay: Initialize LVGL
I (385) LVGL: Starting LVGL task
I (385) OledDisplay: Adding OLED display
I (405) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (405) MCP: Add tool: self.lamp.get_state
I (405) MCP: Add tool: self.lamp.turn_on
I (415) MCP: Add tool: self.lamp.turn_off
I (415) Application: STATE: starting
I (415) Ns4168OutputCodec: NS4168OutputCodec constructor started
I (425) Ns4168OutputCodec: parameters: output_rate=16000
I (425) Ns4168OutputCodec: GPIO: BCLK=15, WS=16, DOUT=7, CTRL=13
I (435) Ns4168OutputCodec: configuring control pin GPIO13
I (435) gpio: GPIO[13]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (445) Ns4168OutputCodec: GPIO configuration succeeded
I (455) Ns4168OutputCodec: control pin initialized to low level (muted state) to prevent startup noise
I (465) Ns4168OutputCodec: delaying I2S channel creation to avoid startup noise
I (465) Ns4168OutputCodec: NS4168OutputCodec constructor completed
I (475) Mems3526AudioCodec: MEMS3526 Microphone constructor started (PDM mode)
I (485) Mems3526AudioCodec: Parameters: sample_rate=16000, clk_pin=5, data_pin=6
I (485) Mems3526AudioCodec: Configuring GPIO pins
I (495) Mems3526AudioCodec: Initializing PDM mode
I (495) Mems3526AudioCodec: PDM configuration: CLK=GPIO5, DATA=GPIO6, sample_rate=16000
I (505) Mems3526AudioCodec: Creating I2S channel with 6 descriptors, 240 frames each
I (515) Mems3526AudioCodec: I2S RX channel created successfully, handle: 0x3c291078
I (515) Mems3526AudioCodec: Initializing PDM RX mode...
I (525) Mems3526AudioCodec: GPIO configuration completed
I (525) Mems3526AudioCodec: PDM mode initialization completed successfully
I (535) Mems3526AudioCodec: MEMS3526 PDM input channel created on I2S0
I (545) Mems3526AudioCodec: MEMS3526 Microphone constructor completed (PDM mode)
I (545) CompositeAudioCodec: CompositeAudioCodec constructor started
I (555) CompositeAudioCodec: Output codec: 0x3fca7b98, Input codec: 0x3fca7b68, Take ownership: false
I (565) CompositeAudioCodec: CompositeAudioCodec constructor completed
I (575) Application: WiFi board detected, setting opus encoder complexity to 0
I (575) CompositeAudioCodec: CompositeAudioCodec starting...
I (585) CompositeAudioCodec: Starting output codec...
I (585) Ns4168OutputCodec: Volume loaded from settings: 70 (default was 70)
I (595) Ns4168OutputCodec: executing delayed I2S initialization
I (605) Ns4168OutputCodec: NS4168 I2S output channel created on I2S1 (mono mode)
I (605) Ns4168OutputCodec: delayed I2S initialization completed
I (615) Ns4168OutputCodec: I2S channel enabled, waiting for stabilization...
I (675) Ns4168OutputCodec: NS4168 amplifier enabled
I (695) AudioCodec: Set output enable to true
I (695) Ns4168OutputCodec: Waiting for audio system to be fully ready...
I (895) Ns4168OutputCodec: Audio system ready: tx_handle=0x3c29bca0, output_enabled=true
I (895) Ns4168OutputCodec: NS4168OutputCodec started successfully and ready for audio output
I (895) CompositeAudioCodec: Starting input codec...
I (895) Mems3526AudioCodec: Starting MEMS3526 Microphone...
I (905) Mems3526AudioCodec: Enabling I2S RX channel...
I (905) Mems3526AudioCodec: I2S RX channel enabled successfully
I (915) Mems3526AudioCodec: MEMS3526 input enabled
I (915) Mems3526AudioCodec: Input enabled status: true
I (925) Mems3526AudioCodec: RX handle: 0x3c291078
I (1025) Mems3526AudioCodec: Read 160 samples from MEMS3526 (requested 160), amplitude range: -679 to 0
I (1025) Mems3526AudioCodec: Test read result: 160 samples
I (1025) Mems3526AudioCodec: MEMS3526 Microphone started successfully
I (1025) CompositeAudioCodec: Enabling CompositeAudioCodec input...
I (1035) CompositeAudioCodec: CompositeAudioCodec EnableInput: enabled
I (1045) Mems3526AudioCodec: MEMS3526 input enabled
I (1045) CompositeAudioCodec: Enabling CompositeAudioCodec output...
I (1055) CompositeAudioCodec: CompositeAudioCodec EnableOutput: enabled
I (1055) CompositeAudioCodec: === CompositeAudioCodec Status ===
I (1065) CompositeAudioCodec: Output Codec: 0x3fca7b98 (ownership: false)
I (1075) CompositeAudioCodec: Input Codec: 0x3fca7b68 (ownership: false)
I (1075) CompositeAudioCodec: Output: enabled=true, sample_rate=16000, channels=1
I (1085) CompositeAudioCodec: Input: enabled=true, sample_rate=16000, channels=1, reference=false
I (1095) CompositeAudioCodec: Volume: 70
I (1095) CompositeAudioCodec: ================================
I (1105) CompositeAudioCodec: CompositeAudioCodec started successfully
I (1115) pp: pp rom version: e7ae62f
I (1115) net80211: net80211 rom version: e7ae62f
I (1125) wifi:wifi driver task: 3fcd6774, prio:23, stack:6144, core=0
I (1125) wifi:wifi firmware version: 79fa3f41ba
I (1125) wifi:wifi certification version: v7.0
I (1135) wifi:config NVS flash: disabled
I (1135) wifi:config nano formatting: enabled
I (1135) wifi:Init data frame dynamic rx buffer num: 8
I (1145) wifi:Init dynamic rx mgmt buffer num: 5
I (1145) wifi:Init management short buffer num: 32
I (1155) wifi:Init dynamic tx buffer num: 32
I (1155) wifi:Init static tx FG buffer num: 2
I (1165) wifi:Init static rx buffer size: 1600
I (1165) wifi:Init static rx buffer num: 6
I (1165) wifi:Init dynamic rx buffer num: 8
I (1175) wifi_init: rx ba win: 6
I (1175) wifi_init: accept mbox: 6
I (1175) wifi_init: tcpip mbox: 32
I (1185) wifi_init: udp mbox: 6
I (1185) wifi_init: tcp mbox: 6
I (1185) wifi_init: tcp tx win: 5760
I (1195) wifi_init: tcp rx win: 5760
I (1195) wifi_init: tcp mss: 1440
I (1195) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
I (1235) wifi:mode : sta (28:37:2f:89:7c:24)
I (1235) wifi:enable tsf
I (3635) wifi: Found AP: ASUS3100_2G, BSSID: 4c:ed:fb:ac:1f:50, RSSI: -51, Channel: 11, Authmode: 3
W (3645) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (5225) wifi:new:<11,0>, old:<1,0>, ap:<255,255>, sta:<11,0>, prof:1, snd_ch_cfg:0x0
I (5225) wifi:state: init -> auth (0xb0)
I (5235) wifi:state: auth -> assoc (0x0)
I (5235) wifi:state: assoc -> run (0x10)
I (5235) wifi:<ba-add>idx:0 (ifx:0, 4c:ed:fb:ac:1f:50), tid:0, ssn:0, winSize:64
I (5255) wifi:connected with ASUS3100_2G, aid = 1, channel 11, BW20, bssid = 4c:ed:fb:ac:1f:50
I (5255) wifi:security: WPA2-PSK, phy: bgn, rssi: -52
I (5265) wifi:pm start, type: 1

I (5265) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (5275) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (5305) wifi:AP's beacon interval = 102400 us, DTIM period = 3
I (6295) wifi: Got IP: ************
I (6295) esp_netif_handlers: sta ip: ************, mask: *************, gw: ***********
I (6305) Application: STATE: activating
I (6305) Ota: Current version: 1.7.5
I (6315) EspHttp: Opening HTTP connection to http://*************:8002/xiaozhi/ota/
I (6405) Ota: No mqtt section found !
I (6405) Ota: Current is the latest version
I (6405) Ota: Running partition: ota_0
I (6405) MCP: Add tool: self.get_device_status
I (6405) MCP: Add tool: self.audio_speaker.set_volume
I (6415) MODEL_LOADER: The storage free size is 21952 KB
I (6415) MODEL_LOADER: The partition size is 960 KB
I (6425) MODEL_LOADER: Successfully load srmodels
I (6425) AFE: AFE Version: (1MIC_V250121)
I (6435) AFE: Input PCM Config: total 1 channels(1 microphone, 0 playback), sample rate:16000
I (6435) AFE: AFE Pipeline: [input] -> |VAD(WebRTC)| -> [output]
I (6445) AfeAudioProcessor: Audio communication task started, feed size: 512 fetch size: 512
I (6455) AfeWakeWord: Model 0: wn9_nihaoxiaozhi_tts
I (6455) AFE_CONFIG: Set WakeNet Model: wn9_nihaoxiaozhi_tts
MC Quantized wakenet9: wakenet9l_tts1h8_你好小智_3_0.631_0.635, tigger:v4, mode:0, p:0, (Jun 16 2025 16:18:15)
I (6495) AFE: AFE Version: (1MIC_V250121)
I (6505) AFE: Input PCM Config: total 1 channels(1 microphone, 0 playback), sample rate:16000
I (6505) AFE: AFE Pipeline: [input] ->  -> |VAD(WebRTC)| -> |WakeNet(wn9_nihaoxiaozhi_tts,)| -> [output]
I (6515) AfeWakeWord: Audio detection task started, feed size: 512 fetch size: 512
I (6525) Application: STATE: idle
I (6535) Mems3526AudioCodec: Read 512 samples from MEMS3526 (requested 512), amplitude range: -296 to 0
I (6535) CompositeAudioCodec: CompositeAudioCodec::Read: InputData returned true for 512 samples
I (6545) Mems3526AudioCodec: Read 512 samples from MEMS3526 (requested 512), amplitude range: -317 to 0
I (6555) Application: ResetDecoder called - current state: idle
I (6555) CompositeAudioCodec: CompositeAudioCodec::Read: InputData returned true for 512 samples
I (6565) Application: ResetDecoder: Skipped enabling output in idle state to prevent noise
I (6575) Mems3526AudioCodec: Read 512 samples from MEMS3526 (requested 512), amplitude range: -256 to 0
I (6575) SystemInfo: free sram: 91967 minimal sram: 91967
I (6585) CompositeAudioCodec: CompositeAudioCodec::Read: InputData returned true for 512 samples
I (6605) Mems3526AudioCodec: Read 512 samples from MEMS3526 (requested 512), amplitude range: -293 to 0
I (6605) CompositeAudioCodec: CompositeAudioCodec::Read: InputData returned true for 512 samples
I (6615) CompositeAudioCodec: CompositeAudioCodec::Read: InputData returned true for 512 samples
I (12795) Application: STATE: connecting
I (12795) WS: Connecting to websocket server: ws://*************:8000/xiaozhi/v1/ with version: 1
I (12795) WebSocket: Connecting to ws://*************:8000/xiaozhi/v1/
I (12855) WS: Session ID: 7969133f-4cb7-4d25-8f80-97a4b95016ee
I (12865) wifi:Set ps type: 0, coexist: 0

I (12865) Application: Wake word detected: 你好小智
I (12905) WS: Session ID: 7969133f-4cb7-4d25-8f80-97a4b95016ee
I (13175) AfeWakeWord: Encode wake word opus 66 packets in 385 ms
I (13175) Application: STATE: listening
I (13215) Application: >> 嘿，你好呀
I (13215) Application: STATE: speaking
I (13235) Application: ResetDecoder called - current state: speaking
I (13235) Application: ResetDecoder: Enabled output (state: speaking)
I (15245) Application: << 你好呀！
I (16335) Application: << 我是好奇男孩，一个8岁的小男孩。
I (19815) Application: << 你有什么问题想问我吗？
I (22085) Application: << 我可以帮你找到很多有趣的知识哦！
I (22825) WinxtecCompactWifiBoard: BUTTON: Boot button clicked
I (22825) WinxtecCompactWifiBoard: BUTTON: Boot button - toggling chat state
I (22825) Application: Abort speaking - reason: 0, current state: 6
I (22875) Application: STATE: listening
I (26295) WinxtecCompactWifiBoard: BUTTON: Boot button clicked
I (26295) WinxtecCompactWifiBoard: BUTTON: Boot button - toggling chat state
E (26295) TcpTransport: Receive failed: -1
I (26305) WS: Websocket disconnected
I (26305) wifi:Set ps type: 1, coexist: 0

I (26305) Application: STATE: idle
I (28675) Application: STATE: connecting
I (28705) WS: Connecting to websocket server: ws://*************:8000/xiaozhi/v1/ with version: 1
I (28705) WebSocket: Connecting to ws://*************:8000/xiaozhi/v1/
I (28755) WS: Session ID: 174d3fe1-a7cb-49b8-8609-72a386dbbac2
I (28755) wifi:Set ps type: 0, coexist: 0

I (28755) Application: Wake word detected: 你好小智
I (28815) WS: Session ID: 174d3fe1-a7cb-49b8-8609-72a386dbbac2
I (29105) AfeWakeWord: Encode wake word opus 66 packets in 431 ms
I (29105) Application: STATE: listening
I (29135) Application: >> 嘿，你好呀
I (29135) Application: STATE: speaking
I (29135) Application: ResetDecoder called - current state: speaking
I (29135) Application: ResetDecoder: Enabled output (state: speaking)
I (31055) Application: << 嗨！
I (31905) Application: << 你好呀，我是好奇男孩，一个8岁的小男孩。
I (36105) Application: << 你有什么问题想问我吗？
I (38385) Application: << 我可以帮你解答哦！
I (39105) SystemInfo: free sram: 47167 minimal sram: 43311
I (40525) Application: STATE: listening
