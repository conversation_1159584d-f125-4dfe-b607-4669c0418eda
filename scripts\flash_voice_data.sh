#!/bin/bash
# ESP32 TTS语音数据烧录脚本 (Linux/macOS版本)
#
# 使用方法:
#   ./flash_voice_data.sh [端口] [语音类型] [目标芯片]
#
# 参数:
#   端口: 串口设备路径 (如 /dev/ttyUSB0, /dev/cu.usbserial-*)，可选
#   语音类型: xiaoxin_small|xiaoxin|xiaole|xiaoxin_custom，默认 xiaoxin_small
#   目标芯片: esp32|esp32s2|esp32s3|esp32c3|esp32c6|esp32c5|esp32p4，默认 esp32s3
#
# 示例:
#   ./flash_voice_data.sh
#   ./flash_voice_data.sh /dev/ttyUSB0
#   ./flash_voice_data.sh /dev/ttyUSB0 xiaoxin_small esp32s3

set -e  # 遇到错误立即退出

# 设置默认值
DEFAULT_VOICE="xiaoxin_small"
DEFAULT_TARGET="esp32s3"
DEFAULT_BAUD="460800"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 解析命令行参数
PORT="$1"
VOICE="${2:-$DEFAULT_VOICE}"
TARGET="${3:-$DEFAULT_TARGET}"

echo "========================================"
echo "ESP32 TTS语音数据烧录脚本"
echo "========================================"
echo

# 检查Python是否可用
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python，请确保Python已安装"
    exit 1
fi

# 优先使用python3
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# 检查ESP-IDF环境
if [ -z "$IDF_PATH" ]; then
    echo "⚠️  警告: 未检测到ESP-IDF环境变量"
    echo "请先运行ESP-IDF的export.sh脚本设置环境"
    echo "例如: source ~/esp/esp-idf/export.sh"
    
    # 询问是否继续
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 1
    fi
fi

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 构建Python脚本参数
PYTHON_ARGS="-v $VOICE -t $TARGET -b $DEFAULT_BAUD"

if [ -n "$PORT" ]; then
    PYTHON_ARGS="$PYTHON_ARGS -p $PORT"
fi

echo "配置信息:"
echo "  端口: ${PORT:-自动检测}"
echo "  语音类型: $VOICE"
echo "  目标芯片: $TARGET"
echo "  波特率: $DEFAULT_BAUD"
echo

# 询问用户确认
read -p "是否继续烧录? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo
echo "开始烧录语音数据..."
echo

# 执行Python脚本
if $PYTHON_CMD scripts/flash_voice_data.py $PYTHON_ARGS; then
    echo
    echo "✅ 烧录完成!"
    echo "现在可以重启设备测试TTS功能。"
else
    echo
    echo "❌ 烧录失败!"
    exit 1
fi
