@echo off
REM ESP32 TTS Voice Data Flash Script (Windows Batch Version)
REM
REM Usage:
REM   flash_voice_data.bat [port] [voice_type] [target_chip]
REM
REM Parameters:
REM   port: COM port number (like COM3, COM4), optional
REM   voice_type: xiaoxin_small|xiaoxin|xiaole|xiaoxin_custom, default xiaoxin_small
REM   target_chip: esp32|esp32s2|esp32s3|esp32c3|esp32c6|esp32c5|esp32p4, default esp32s3
REM
REM Examples:
REM   flash_voice_data.bat
REM   flash_voice_data.bat COM3
REM   flash_voice_data.bat COM3 xiaoxin_small esp32s3

setlocal enabledelayedexpansion

REM 设置默认值
set DEFAULT_VOICE=xiaoxin_small
set DEFAULT_TARGET=esp32s3
set DEFAULT_BAUD=460800

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..

REM 解析命令行参数
set PORT=%1
set VOICE=%2
set TARGET=%3

REM 设置默认值
if "%VOICE%"=="" set VOICE=%DEFAULT_VOICE%
if "%TARGET%"=="" set TARGET=%DEFAULT_TARGET%

echo ========================================
echo ESP32 TTS语音数据烧录脚本
echo ========================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请确保Python已安装并添加到PATH中
    pause
    exit /b 1
)

REM 检查ESP-IDF环境
if "%IDF_PATH%"=="" (
    echo ⚠️  警告: 未检测到ESP-IDF环境变量
    echo 尝试使用默认ESP-IDF路径...
    
    REM 尝试常见的ESP-IDF安装路径
    if exist "D:\Espressif\frameworks\esp-idf-v5.4.1\export.bat" (
        echo 找到ESP-IDF: D:\Espressif\frameworks\esp-idf-v5.4.1
        call "D:\Espressif\frameworks\esp-idf-v5.4.1\export.bat"
    ) else if exist "C:\Espressif\frameworks\esp-idf-v5.4.1\export.bat" (
        echo 找到ESP-IDF: C:\Espressif\frameworks\esp-idf-v5.4.1
        call "C:\Espressif\frameworks\esp-idf-v5.4.1\export.bat"
    ) else (
        echo ❌ 错误: 未找到ESP-IDF环境
        echo 请先运行ESP-IDF的export.bat脚本设置环境
        pause
        exit /b 1
    )
)

REM 切换到项目根目录
cd /d "%PROJECT_ROOT%"

REM 构建Python脚本参数
set PYTHON_ARGS=-v %VOICE% -t %TARGET% -b %DEFAULT_BAUD%

if not "%PORT%"=="" (
    set PYTHON_ARGS=%PYTHON_ARGS% -p %PORT%
)

echo 配置信息:
echo   端口: %PORT%
echo   语音类型: %VOICE%
echo   目标芯片: %TARGET%
echo   波特率: %DEFAULT_BAUD%
echo.

REM 询问用户确认
set /p CONFIRM=是否继续烧录? (y/N): 
if /i not "%CONFIRM%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 开始烧录语音数据...
echo.

REM 执行Python脚本
python scripts\flash_voice_data.py %PYTHON_ARGS%

if errorlevel 1 (
    echo.
    echo ❌ 烧录失败!
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 烧录完成!
    echo 现在可以重启设备测试TTS功能。
    pause
    exit /b 0
)
