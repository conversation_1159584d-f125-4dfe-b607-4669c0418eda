#include "ns4168_output_codec.h"

#include <esp_log.h>
#include <cstring>
#include <vector>
#include <algorithm>
#include "settings.h"

#define TAG "Ns4168OutputCodec"

Ns4168OutputCodec::Ns4168OutputCodec(int output_sample_rate,
    gpio_num_t spk_bclk, gpio_num_t spk_ws, gpio_num_t spk_dout, gpio_num_t spk_en) : ctrl_pin_(spk_en) {

    duplex_ = false; // 仅支持输出
    input_reference_ = false;
    input_channels_ = 0; // 不支持输入
    input_sample_rate_ = 0;
    output_sample_rate_ = output_sample_rate;

    ESP_LOGI(TAG, "NS4168OutputCodec constructor started");
    ESP_LOGI(TAG, "parameters: output_rate=%d", output_sample_rate);
    ESP_LOGI(TAG, "GPIO: BCLK=%d, WS=%d, DOUT=%d, CTRL=%d", spk_bclk, spk_ws, spk_dout, spk_en);

    // 配置控制引脚（功放使能）
    if (ctrl_pin_ != GPIO_NUM_NC) {
        ESP_LOGI(TAG, "configuring control pin GPIO%d", ctrl_pin_);
        gpio_config_t gpio_cfg = {
            .pin_bit_mask = (1ULL << ctrl_pin_),
            .mode = GPIO_MODE_OUTPUT,
            .pull_up_en = GPIO_PULLUP_DISABLE,
            .pull_down_en = GPIO_PULLDOWN_DISABLE,
            .intr_type = GPIO_INTR_DISABLE,
        };
        esp_err_t ret = gpio_config(&gpio_cfg);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "GPIO configuration failed: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "GPIO configuration succeeded");
        }

        // 初始化时先设置为低电平（静音状态），避免开机爆音
        ret = gpio_set_level(ctrl_pin_, 0);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "setting GPIO level failed: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "control pin initialized to low level (muted state) to prevent startup noise");
        }
    } else {
        ESP_LOGW(TAG, "control pin not configured");
    }

    ESP_LOGI(TAG, "delaying I2S channel creation to avoid startup noise");
    // 延迟I2S初始化 - 保存配置参数，在需要时再创建
    bclk_pin_ = spk_bclk;
    ws_pin_ = spk_ws;
    dout_pin_ = spk_dout;
    i2s_initialized_ = false;

    // 初始化时禁用输出，避免启动时的电流声
    output_enabled_ = false;
    ESP_LOGI(TAG, "NS4168OutputCodec constructor completed");
}

Ns4168OutputCodec::~Ns4168OutputCodec() {
    ESP_LOGI(TAG, "NS4168OutputCodec destructor starting - cleaning up I2S resources");

    if (tx_handle_ != nullptr) {
        esp_err_t ret = i2s_channel_disable(tx_handle_);
        if (ret != ESP_OK) {
            ESP_LOGW(TAG, "Failed to disable TX channel: %s", esp_err_to_name(ret));
        }

        ret = i2s_del_channel(tx_handle_);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "TX channel deleted successfully");
        } else {
            ESP_LOGE(TAG, "Failed to delete TX channel: %s", esp_err_to_name(ret));
        }
        tx_handle_ = nullptr;
    }

    ESP_LOGI(TAG, "NS4168OutputCodec destructor completed - all I2S resources cleaned up");
}

void Ns4168OutputCodec::EnsureI2sInitialized() {
    if (!i2s_initialized_) {
        ESP_LOGI(TAG, "executing delayed I2S initialization");
        CreateI2sOutputChannel(bclk_pin_, ws_pin_, dout_pin_);
        i2s_initialized_ = true;
        ESP_LOGI(TAG, "delayed I2S initialization completed");
    }
}

void Ns4168OutputCodec::CreateI2sOutputChannel(gpio_num_t bclk, gpio_num_t ws, gpio_num_t dout) {
    // I2S 输出通道配置 (NS4168) - 使用I2S1，避免与MEMS3526 PDM(I2S0)冲突
    i2s_chan_config_t chan_cfg = I2S_CHANNEL_DEFAULT_CONFIG(I2S_NUM_1, I2S_ROLE_MASTER);
    chan_cfg.dma_desc_num = AUDIO_CODEC_DMA_DESC_NUM;
    chan_cfg.dma_frame_num = AUDIO_CODEC_DMA_FRAME_NUM;
    ESP_ERROR_CHECK(i2s_new_channel(&chan_cfg, &tx_handle_, nullptr));

    // 配置I2S为单声道模式 - NS4168是单声道功放
    i2s_std_slot_config_t slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(I2S_DATA_BIT_WIDTH_16BIT, I2S_SLOT_MODE_MONO);

    i2s_std_config_t std_cfg = {
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(static_cast<uint32_t>(output_sample_rate_)),
        .slot_cfg = slot_cfg,
        .gpio_cfg = {
            .mclk = I2S_GPIO_UNUSED,
            .bclk = bclk,
            .ws = ws,
            .dout = dout,
            .din = I2S_GPIO_UNUSED,
            .invert_flags = {
                .mclk_inv = false,
                .bclk_inv = false,
                .ws_inv = false,
            },
        },
    };
    ESP_ERROR_CHECK(i2s_channel_init_std_mode(tx_handle_, &std_cfg));

    ESP_LOGI(TAG, "NS4168 I2S output channel created on I2S1 (mono mode)");
}

int Ns4168OutputCodec::Write(const int16_t* data, int samples) {
    if (!output_enabled_) {
        // 如果输出被禁用，返回写入的样本数但不实际写入
        ESP_LOGW(TAG, "I2S output disabled, skipping write operation");
        return samples;
    }

    if (!tx_handle_) {
        ESP_LOGW(TAG, "I2S output channel not initialized");
        return samples;
    }
    // NS4168单声道功放，直接写入原始数据（音量控制在硬件层面处理）
    size_t bytes_written;
    esp_err_t ret = i2s_channel_write(tx_handle_, data, samples * sizeof(int16_t), &bytes_written, portMAX_DELAY);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2S write failed: %s", esp_err_to_name(ret));
        return 0;
    }

    return samples;  // 返回原始样本数
}

int Ns4168OutputCodec::Read(int16_t* dest, int samples) {
    // NS4168仅支持输出，不支持输入
    ESP_LOGW(TAG, "Read operation not supported - NS4168 is output-only");
    return 0;
}

void Ns4168OutputCodec::Start() {
    // 加载音频设置
    Settings settings("audio", false);
    int old_volume = output_volume_;
    output_volume_ = settings.GetInt("output_volume", output_volume_);
    ESP_LOGI(TAG, "Volume loaded from settings: %d (default was %d)", output_volume_, old_volume);
    if (output_volume_ <= 0) {
        ESP_LOGW(TAG, "Output volume value (%d) is too small, setting to default (10)", output_volume_);
        output_volume_ = 10;
    }

    // 确保I2S已初始化，但不启用输出（延迟启用机制）
    EnsureI2sInitialized();

    // 不在Start()中启用输出，而是等待第一次播放音频时再启用
    // 这样可以避免启动噪音
    ESP_LOGI(TAG, "NS4168OutputCodec initialized but output not enabled (delayed activation)");

    // 测试I2S通道是否正常创建
    if (tx_handle_) {
        ESP_LOGI(TAG, "I2S system ready: tx_handle=%p, output_enabled=%s",
                 tx_handle_, output_enabled_ ? "true" : "false");
    } else {
        ESP_LOGW(TAG, "I2S system not ready: tx_handle=%p", tx_handle_);
    }

    ESP_LOGI(TAG, "NS4168OutputCodec started successfully and ready for audio output");
}

void Ns4168OutputCodec::SetOutputVolume(int volume) {
    output_volume_ = volume;
    ESP_LOGI(TAG, "NS4168 output volume set to %d", volume);
}

void Ns4168OutputCodec::EnableInput(bool enable) {
    // NS4168仅支持输出，不支持输入
    if (enable) {
        ESP_LOGW(TAG, "Input enable not supported - NS4168 is output-only");
    }
}

void Ns4168OutputCodec::EnableOutput(bool enable) {
    if (enable == output_enabled_) {
        return;
    }

    if (enable) {
        // 确保I2S通道已初始化
        EnsureI2sInitialized();

        // 启用I2S通道（检查状态避免重复启用）
        if (tx_handle_) {
            esp_err_t ret = i2s_channel_enable(tx_handle_);
            if (ret == ESP_ERR_INVALID_STATE) {
                ESP_LOGW(TAG, "I2S channel already enabled");
            } else if (ret != ESP_OK) {
                ESP_LOGE(TAG, "Failed to enable I2S channel: %s", esp_err_to_name(ret));
            } else {
                // I2S通道刚启用，等待稳定
                ESP_LOGI(TAG, "I2S channel enabled, waiting for stabilization...");

                vTaskDelay(pdMS_TO_TICKS(50));
            }
        }

        // 启用NS4168功放
        if (ctrl_pin_ != GPIO_NUM_NC) {
            ESP_LOGI(TAG, "Enabling NS4168 amplifier after I2S stabilization...");
            gpio_set_level(ctrl_pin_, 1);
            ESP_LOGI(TAG, "NS4168 amplifier enabled");
            // 等待功放稳定，增加延迟确保无噪音启动
            vTaskDelay(pdMS_TO_TICKS(50));
        }
    } else {

        
        // 禁用NS4168功放
        if (ctrl_pin_ != GPIO_NUM_NC) {
            gpio_set_level(ctrl_pin_, 0);
            ESP_LOGI(TAG, "NS4168 amplifier disabled");
        }

        // 禁用I2S通道
        if (tx_handle_) {
            esp_err_t ret = i2s_channel_disable(tx_handle_);
            if (ret != ESP_OK) {
                ESP_LOGW(TAG, "Failed to disable I2S channel: %s", esp_err_to_name(ret));
            }
        }
    }

    AudioCodec::EnableOutput(enable);
}

