#ifndef _COMPOSITE_AUDIO_CODEC_H_
#define _COMPOSITE_AUDIO_CODEC_H_

#include "audio_codec.h"

/**
 * @brief 组合音频编解码器类
 * 
 * 支持将不同的音频输入和输出设备组合使用：
 * - NS4168: 音频输出（扬声器）
 * - MEMS3526: 音频输入（麦克风）
 * 
 * 这个类将两个独立的音频设备组合成一个统一的AudioCodec接口
 */
class CompositeAudioCodec : public AudioCodec {
private:
    AudioCodec* output_codec_;  // 输出设备（如NS4168）
    AudioCodec* input_codec_;   // 输入设备（如MEMS3526）
    
    bool owns_output_codec_;    // 是否拥有输出编解码器的所有权
    bool owns_input_codec_;     // 是否拥有输入编解码器的所有权
    
public:
    /**
     * @brief 构造函数
     * @param output_codec 输出音频编解码器（可以为nullptr）
     * @param input_codec 输入音频编解码器（可以为nullptr）
     * @param take_ownership 是否接管编解码器的所有权（默认true）
     */
    CompositeAudioCodec(AudioCodec* output_codec, 
                       AudioCodec* input_codec, 
                       bool take_ownership = true);
    
    virtual ~CompositeAudioCodec();
    
    // 实现AudioCodec的纯虚函数
    virtual int Read(int16_t* dest, int samples) override;
    virtual int Write(const int16_t* data, int samples) override;
    
    // 重写音频控制方法
    virtual void SetOutputVolume(int volume) override;
    virtual void EnableInput(bool enable) override;
    virtual void EnableOutput(bool enable) override;
    
    // 重写Start方法
    void Start();
    
    // 获取子编解码器
    AudioCodec* GetOutputCodec() const { return output_codec_; }
    AudioCodec* GetInputCodec() const { return input_codec_; }
    
    // 设置子编解码器
    void SetOutputCodec(AudioCodec* codec, bool take_ownership = true);
    void SetInputCodec(AudioCodec* codec, bool take_ownership = true);
    
    // 调试信息
    void PrintStatus();
};

#endif // _COMPOSITE_AUDIO_CODEC_H_
