# ESP32 TTS语音数据烧录脚本

本目录包含用于单独烧录TTS语音数据到ESP32设备的脚本工具。

## 📁 文件说明

- `flash_voice_data.py` - Python主脚本，跨平台支持
- `flash_voice_data.bat` - Windows批处理脚本
- `flash_voice_data.sh` - Linux/macOS shell脚本
- `README_voice_data.md` - 本说明文档

## 🚀 快速开始

### Windows用户

1. **双击运行**（推荐）：
   ```cmd
   双击 flash_voice_data.bat
   ```

2. **命令行运行**：
   ```cmd
   flash_voice_data.bat [COM端口] [语音类型] [目标芯片]
   ```

### Linux/macOS用户

1. **添加执行权限**：
   ```bash
   chmod +x flash_voice_data.sh
   ```

2. **运行脚本**：
   ```bash
   ./flash_voice_data.sh [端口] [语音类型] [目标芯片]
   ```

### 直接使用Python脚本

```bash
python scripts/flash_voice_data.py [选项]
```

## 📋 参数说明

### 语音类型 (voice)
- `xiaole` - 小乐语音（女性音色，活泼清脆）**[默认]**
- `xiaoxin_small` - 小新语音（小体积版本）
- `xiaoxin` - 小新语音（完整版本）
- `xiaoxin_custom` - 小新语音（自定义版本）

### 目标芯片 (target)
- `esp32s3` - ESP32-S3 **[默认]**
- `esp32` - ESP32
- `esp32s2` - ESP32-S2
- `esp32c3` - ESP32-C3
- `esp32c6` - ESP32-C6
- `esp32c5` - ESP32-C5
- `esp32p4` - ESP32-P4

### 串口端口
- **Windows**: `COM3`, `COM4`, `COM5` 等
- **Linux**: `/dev/ttyUSB0`, `/dev/ttyACM0` 等
- **macOS**: `/dev/cu.usbserial-*` 等

## 💡 使用示例

### 基本使用（自动检测）
```bash
# Windows
flash_voice_data.bat

# Linux/macOS
./flash_voice_data.sh

# Python
python scripts/flash_voice_data.py
```

### 指定端口
```bash
# Windows
flash_voice_data.bat COM3

# Linux/macOS
./flash_voice_data.sh /dev/ttyUSB0

# Python
python scripts/flash_voice_data.py -p COM3
```

### 指定语音类型和芯片
```bash
# Windows
flash_voice_data.bat COM3 xiaole esp32s3

# Linux/macOS
./flash_voice_data.sh /dev/ttyUSB0 xiaole esp32s3

# Python
python scripts/flash_voice_data.py -p COM3 -v xiaole -t esp32s3
```

### 干运行模式（仅显示命令）
```bash
python scripts/flash_voice_data.py --dry-run
```

## 🔧 Python脚本完整选项

```bash
python scripts/flash_voice_data.py [选项]

选项:
  -p, --port PORT         串口端口 (默认: 自动检测)
  -b, --baud BAUD         波特率 (默认: 460800)
  -v, --voice VOICE       语音类型 (xiaoxin_small|xiaoxin|xiaole|xiaoxin_custom)
  -t, --target TARGET     目标芯片 (esp32|esp32s2|esp32s3|esp32c3|esp32c6|esp32c5|esp32p4)
  --partition-table FILE  分区表文件路径
  --dry-run              仅显示将要执行的命令，不实际烧录
  -h, --help             显示帮助信息
```

## 📊 语音数据文件大小对比

| 语音类型 | 文件大小 | 说明 |
|---------|---------|------|
| xiaole | ~2.9MB | 小乐语音，女性音色，活泼清脆 **[默认]** |
| xiaoxin_small | ~2.9MB | 小新语音，小体积版本，自然稳重 |
| xiaoxin | ~3.8MB | 小新语音，完整版本，音质更好 |
| xiaoxin_custom | ~3.8MB | 小新语音，自定义版本 |

## ⚠️ 注意事项

1. **ESP-IDF环境**：
   - 确保已正确安装ESP-IDF环境
   - Windows用户需要运行`export.bat`
   - Linux/macOS用户需要运行`source export.sh`

2. **分区表要求**：
   - 确保分区表中包含`voice_data`分区
   - 分区大小至少4MB以容纳语音数据

3. **设备连接**：
   - 确保ESP32设备已正确连接到电脑
   - 检查串口驱动是否正确安装

4. **权限问题**：
   - Linux/macOS可能需要sudo权限访问串口
   - 或将用户添加到dialout组：`sudo usermod -a -G dialout $USER`

## 🐛 故障排除

### 常见错误及解决方案

1. **"未找到ESP-SR组件路径"**
   - 检查`components/espressif__esp-sr`或`managed_components/espressif__esp-sr`是否存在
   - 运行`idf.py reconfigure`重新配置项目

2. **"语音数据文件不存在"**
   - 检查ESP-SR组件是否完整下载
   - 尝试重新下载ESP-SR组件

3. **"在分区表中未找到voice_data分区"**
   - 检查分区表文件是否包含voice_data分区
   - 参考项目中的分区表示例添加voice_data分区

4. **"串口权限被拒绝"**
   - Linux/macOS: `sudo chmod 666 /dev/ttyUSB0`
   - 或添加用户到dialout组

5. **"esptool命令失败"**
   - 检查ESP-IDF环境是否正确设置
   - 确保设备处于下载模式
   - 尝试降低波特率：`-b 115200`

## 📝 开发说明

脚本会自动：
- 检测目标芯片类型（从sdkconfig读取）
- 查找合适的分区表文件
- 解析voice_data分区的偏移地址和大小
- 选择对应的语音数据文件
- 执行esptool烧录命令

如需自定义，可以直接修改Python脚本或传递相应参数。
