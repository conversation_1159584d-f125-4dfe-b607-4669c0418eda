#include "composite_audio_codec.h"
#include <esp_log.h>
#include <cstring>

static const char TAG[] = "CompositeAudioCodec";

CompositeAudioCodec::CompositeAudioCodec(AudioCodec* output_codec, 
                                         AudioCodec* input_codec, 
                                         bool take_ownership)
    : AudioCodec(),
      output_codec_(output_codec),
      input_codec_(input_codec),
      owns_output_codec_(take_ownership),
      owns_input_codec_(take_ownership) {
    
    ESP_LOGI(TAG, "CompositeAudioCodec constructor started");
    ESP_LOGI(TAG, "Output codec: %p, Input codec: %p, Take ownership: %s", 
             output_codec_, input_codec_, take_ownership ? "true" : "false");
    
    // 设置采样率
    if (output_codec_) {
        output_sample_rate_ = output_codec_->output_sample_rate();
        output_channels_ = output_codec_->output_channels();
    }
    
    if (input_codec_) {
        input_sample_rate_ = input_codec_->input_sample_rate();
        input_channels_ = input_codec_->input_channels();
        input_reference_ = input_codec_->input_reference();
    }
    
    ESP_LOGI(TAG, "CompositeAudioCodec constructor completed");
}

CompositeAudioCodec::~CompositeAudioCodec() {
    ESP_LOGI(TAG, "CompositeAudioCodec destructor started");
    
    if (owns_output_codec_ && output_codec_) {
        delete output_codec_;
        output_codec_ = nullptr;
    }
    
    if (owns_input_codec_ && input_codec_) {
        delete input_codec_;
        input_codec_ = nullptr;
    }
    
    ESP_LOGI(TAG, "CompositeAudioCodec destructor completed");
}

int CompositeAudioCodec::Read(int16_t* dest, int samples) {
    static int debug_count = 0;

    // 修复：检查当前状态，如果不是input_enabled_状态，不要启用输出
    if (!input_enabled_) {
        // 输入未启用，填充静音
        memset(dest, 0, samples * sizeof(int16_t));
        return samples;
    }

    if (input_codec_) {
        // 使用InputData方法，它是public的，内部会调用Read
        std::vector<int16_t> buffer(samples);
        bool success = input_codec_->InputData(buffer);

        if (debug_count < 5) {
            ESP_LOGI(TAG, "CompositeAudioCodec::Read: InputData returned %s for %d samples",
                     success ? "true" : "false", samples);
            debug_count++;
        }

        if (success) {
            std::copy(buffer.begin(), buffer.end(), dest);
            return samples;
        } else {
            ESP_LOGW(TAG, "CompositeAudioCodec::Read: read failed, filling silence");
            memset(dest, 0, samples * sizeof(int16_t));
            return samples;  // 修改：返回samples而不是0
        }
    } else {
        if (debug_count < 3) {
            ESP_LOGW(TAG, "CompositeAudioCodec::Read: No input codec available, filling silence");
            debug_count++;
        }
        // 如果没有输入编解码器，填充静音
        memset(dest, 0, samples * sizeof(int16_t));
        return samples;
    }
}

int CompositeAudioCodec::Write(const int16_t* data, int samples) {
    if (output_codec_) {
        // 直接写入，不在这里检查启用状态，避免循环调用
        // 音频输出的启用应该在更高层次（如PlaySound）中处理
        std::vector<int16_t> buffer(data, data + samples);
        output_codec_->OutputData(buffer);
        return samples;
    } else {
        ESP_LOGW(TAG, "CompositeAudioCodec::Write: No output codec available");
        return samples;
    }
}

void CompositeAudioCodec::SetOutputVolume(int volume) {
    output_volume_ = volume;
    ESP_LOGI(TAG, "Set composite output volume to %d", volume);
    
    if (output_codec_) {
        output_codec_->SetOutputVolume(volume);
    }
}

void CompositeAudioCodec::EnableInput(bool enable) {
    if (enable == input_enabled_) {
        return;
    }
    
    ESP_LOGI(TAG, "CompositeAudioCodec EnableInput: %s", enable ? "enabled" : "disabled");
    
    if (input_codec_) {
        input_codec_->EnableInput(enable);
    } else {
        ESP_LOGW(TAG, "No input codec available");
    }
    
    input_enabled_ = enable;
}

void CompositeAudioCodec::EnableOutput(bool enable) {
    if (enable == output_enabled_) {
        return;
    }

    ESP_LOGI(TAG, "CompositeAudioCodec EnableOutput: %s", enable ? "enabled" : "disabled");

    if (output_codec_) {
        output_codec_->EnableOutput(enable);
    } else {
        ESP_LOGW(TAG, "No output codec available");
    }

    output_enabled_ = enable;
}

void CompositeAudioCodec::Start() {
    ESP_LOGI(TAG, "CompositeAudioCodec starting...");
    
    // 启动输出编解码器
    if (output_codec_) {
        ESP_LOGI(TAG, "Starting output codec...");
        output_codec_->Start();
    }
    
    // 启动输入编解码器
    if (input_codec_) {
        ESP_LOGI(TAG, "Starting input codec...");
        input_codec_->Start();

        // 启用CompositeAudioCodec的输入
        ESP_LOGI(TAG, "Enabling CompositeAudioCodec input...");
        EnableInput(true);
    }

    // 启用CompositeAudioCodec的输出（如果有输出编解码器）
    if (output_codec_) {
        ESP_LOGI(TAG, "Enabling CompositeAudioCodec output...");
        EnableOutput(true);
    }

    // 不调用基类的Start方法，因为CompositeAudioCodec本身没有I2S句柄
    // 音量等设置通过子编解码器处理

    // 打印状态信息用于调试
    PrintStatus();

    ESP_LOGI(TAG, "CompositeAudioCodec started successfully");
}

void CompositeAudioCodec::SetOutputCodec(AudioCodec* codec, bool take_ownership) {
    if (owns_output_codec_ && output_codec_) {
        delete output_codec_;
    }
    
    output_codec_ = codec;
    owns_output_codec_ = take_ownership;
    
    if (output_codec_) {
        output_sample_rate_ = output_codec_->output_sample_rate();
        output_channels_ = output_codec_->output_channels();
    }
    
    ESP_LOGI(TAG, "Output codec set to %p, ownership: %s", codec, take_ownership ? "true" : "false");
}

void CompositeAudioCodec::SetInputCodec(AudioCodec* codec, bool take_ownership) {
    if (owns_input_codec_ && input_codec_) {
        delete input_codec_;
    }
    
    input_codec_ = codec;
    owns_input_codec_ = take_ownership;
    
    if (input_codec_) {
        input_sample_rate_ = input_codec_->input_sample_rate();
        input_channels_ = input_codec_->input_channels();
        input_reference_ = input_codec_->input_reference();
    }
    
    ESP_LOGI(TAG, "Input codec set to %p, ownership: %s", codec, take_ownership ? "true" : "false");
}

void CompositeAudioCodec::PrintStatus() {
    ESP_LOGI(TAG, "=== CompositeAudioCodec Status ===");
    ESP_LOGI(TAG, "Output Codec: %p (ownership: %s)", output_codec_, owns_output_codec_ ? "true" : "false");
    ESP_LOGI(TAG, "Input Codec: %p (ownership: %s)", input_codec_, owns_input_codec_ ? "true" : "false");
    ESP_LOGI(TAG, "Output: enabled=%s, sample_rate=%d, channels=%d", 
             output_enabled_ ? "true" : "false", output_sample_rate_, output_channels_);
    ESP_LOGI(TAG, "Input: enabled=%s, sample_rate=%d, channels=%d, reference=%s", 
             input_enabled_ ? "true" : "false", input_sample_rate_, input_channels_, 
             input_reference_ ? "true" : "false");
    ESP_LOGI(TAG, "Volume: %d", output_volume_);
    ESP_LOGI(TAG, "================================");
}
