#include "local_tts_provider.h"
#include <esp_log.h>

#define TAG "LocalTTS"

LocalTtsProvider::LocalTtsProvider() 
#ifdef CONFIG_ESP_TTS_ENABLE
    : tts_handle_(nullptr), voice_(nullptr), voice_data_(nullptr), initialized_(false)
#endif
{
}

LocalTtsProvider::~LocalTtsProvider() {
    Cleanup();
}

bool LocalTtsProvider::Initialize() {
#ifdef CONFIG_ESP_TTS_ENABLE
    if (initialized_) {
        return true;
    }

    // 查找语音数据分区
    const esp_partition_t* part = esp_partition_find_first(ESP_PARTITION_TYPE_DATA, 
                                                          ESP_PARTITION_SUBTYPE_DATA_FAT, 
                                                          "voice_data");
    if (part == nullptr) {
        ESP_LOGE(TAG, "Couldn't find voice data partition!");
        return false;
    }

    // 映射语音数据
    esp_err_t err = esp_partition_mmap(part, 0, part->size, SPI_FLASH_MMAP_DATA, 
                                      (const void**)&voice_data_, &mmap_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to map voice data partition: %s", esp_err_to_name(err));
        return false;
    }

    // 初始化语音模型
    voice_ = esp_tts_voice_set_init(&esp_tts_voice_template, voice_data_);
    if (voice_ == nullptr) {
        ESP_LOGE(TAG, "Failed to initialize voice model");
        esp_partition_munmap(mmap_);
        voice_data_ = nullptr;
        return false;
    }

    // 创建TTS句柄
    tts_handle_ = esp_tts_create(voice_);
    if (tts_handle_ == nullptr) {
        ESP_LOGE(TAG, "Failed to create TTS handle");
        esp_tts_voice_set_free(voice_);
        voice_ = nullptr;
        esp_partition_munmap(mmap_);
        voice_data_ = nullptr;
        return false;
    }

    initialized_ = true;
    ESP_LOGI(TAG, "Local TTS initialized successfully");
    return true;
#else
    ESP_LOGW(TAG, "ESP-TTS not enabled in config");
    return false;
#endif
}

bool LocalTtsProvider::IsAvailable() const {
#ifdef CONFIG_ESP_TTS_ENABLE
    return initialized_ && tts_handle_ != nullptr;
#else
    return false;
#endif
}

std::vector<int16_t> LocalTtsProvider::SynthesizeText(const std::string& text) {
    std::vector<int16_t> audio_data;

#ifdef CONFIG_ESP_TTS_ENABLE
    if (!IsAvailable()) {
        ESP_LOGW(TAG, "TTS not available, initialize first");
        return audio_data;
    }

    // 解析中文文本
    if (!esp_tts_parse_chinese(tts_handle_, text.c_str())) {
        ESP_LOGE(TAG, "Failed to parse text: %s", text.c_str());
        return audio_data;
    }

    // 合成语音数据
    int len[1] = {0};
    do {
        short *data = esp_tts_stream_play(tts_handle_, len, 4);
        if (len[0] > 0 && data != nullptr) {
            // 将数据添加到返回的向量中
            size_t start_pos = audio_data.size();
            audio_data.resize(start_pos + len[0]);
            memcpy(audio_data.data() + start_pos, data, len[0] * sizeof(int16_t));
        }
    } while (len[0] > 0);

    ESP_LOGI(TAG, "Synthesized %zu samples for text: %s", audio_data.size(), text.c_str());
#else
    ESP_LOGW(TAG, "ESP-TTS not enabled in config");
#endif

    return audio_data;
}

void LocalTtsProvider::Cleanup() {
#ifdef CONFIG_ESP_TTS_ENABLE
    if (tts_handle_) {
        esp_tts_destroy(tts_handle_);
        tts_handle_ = nullptr;
    }
    
    if (voice_) {
        esp_tts_voice_set_free(voice_);
        voice_ = nullptr;
    }
    
    if (voice_data_) {
        esp_partition_munmap(mmap_);
        voice_data_ = nullptr;
    }
    
    initialized_ = false;
    ESP_LOGI(TAG, "Local TTS resources cleaned up");
#endif
}

std::vector<std::string> LocalTtsProvider::GetSupportedLanguages() {
    std::vector<std::string> languages;
    languages.push_back("zh-CN"); // 中文
    return languages;
}
