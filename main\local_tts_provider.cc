#include "local_tts_provider.h"
#include <esp_log.h>
#include <cstring>

#define TAG "LocalTTS"

LocalTtsProvider::LocalTtsProvider() 
#ifdef CONFIG_ESP_TTS_ENABLE
    : tts_handle_(nullptr), voice_(nullptr), voice_data_(nullptr), initialized_(false)
#endif
{
}

LocalTtsProvider::~LocalTtsProvider() {
    Cleanup();
}

bool LocalTtsProvider::Initialize() {
#ifdef CONFIG_ESP_TTS_ENABLE
    if (initialized_) {
        return true;
    }

    ESP_LOGI(TAG, "Initializing TTS with safe mode to prevent cache access errors");

    // 添加延时，确保系统完全启动
    vTaskDelay(pdMS_TO_TICKS(100));

    // 查找语音数据分区
    const esp_partition_t* part = esp_partition_find_first(ESP_PARTITION_TYPE_DATA,
                                                          ESP_PARTITION_SUBTYPE_DATA_FAT,
                                                          "voice_data");
    if (part == nullptr) {
        ESP_LOGE(TAG, "Couldn't find voice data partition!");
        return false;
    }

    ESP_LOGI(TAG, "Found voice data partition, size: %zu bytes", part->size);

    // 映射语音数据，使用更安全的方式
    esp_err_t err = esp_partition_mmap(part, 0, part->size, ESP_PARTITION_MMAP_DATA,
                                      (const void**)&voice_data_, &mmap_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to map voice data partition: %s", esp_err_to_name(err));
        return false;
    }

    ESP_LOGI(TAG, "Voice data partition mapped successfully");

    // 初始化语音模型，根据配置选择不同的模型
#if CONFIG_ESP_TTS_VOICE_XIAOLE
    voice_ = esp_tts_voice_set_init(&esp_tts_voice_xiaole, voice_data_);
    ESP_LOGI(TAG, "Using Xiaole voice model");
#elif CONFIG_ESP_TTS_VOICE_TEMPLATE
    voice_ = esp_tts_voice_set_init(&esp_tts_voice_template, voice_data_);
    ESP_LOGI(TAG, "Using template voice model");
#else
    // 默认使用xiaole模型
    voice_ = esp_tts_voice_set_init(&esp_tts_voice_xiaole, voice_data_);
    ESP_LOGI(TAG, "Using default Xiaole voice model");
#endif
    if (voice_ == nullptr) {
        ESP_LOGE(TAG, "Failed to initialize voice model");
        esp_partition_munmap(mmap_);
        voice_data_ = nullptr;
        return false;
    }

    // 创建TTS句柄
    tts_handle_ = esp_tts_create(voice_);
    if (tts_handle_ == nullptr) {
        ESP_LOGE(TAG, "Failed to create TTS handle");
        esp_tts_voice_set_free(voice_);
        voice_ = nullptr;
        esp_partition_munmap(mmap_);
        voice_data_ = nullptr;
        return false;
    }

    initialized_ = true;
    ESP_LOGI(TAG, "Local TTS initialized successfully");
    return true;
#else
    ESP_LOGW(TAG, "ESP-TTS not enabled in config");
    return false;
#endif
}

bool LocalTtsProvider::IsAvailable() const {
#ifdef CONFIG_ESP_TTS_ENABLE
    return initialized_ && tts_handle_ != nullptr;
#else
    return false;
#endif
}

std::vector<int16_t> LocalTtsProvider::SynthesizeText(const std::string& text) {
    std::vector<int16_t> audio_data;

#ifdef CONFIG_ESP_TTS_ENABLE
    // 暂时移除中断上下文检查，避免编译错误
    // 确保不在中断上下文中执行
    // if (xPortInIsrContext()) {
    //     ESP_LOGE(TAG, "SynthesizeText called from ISR context, this is not allowed!");
    //     return audio_data;
    // }

    if (!IsAvailable()) {
        ESP_LOGW(TAG, "TTS not available, initialize first");
        return audio_data;
    }

    // 添加输入验证
    if (text.empty()) {
        ESP_LOGW(TAG, "Empty text provided for synthesis");
        return audio_data;
    }

    ESP_LOGI(TAG, "Synthesizing text: %s", text.c_str());

    try {
        // 解析中文文本，添加更多安全检查
        ESP_LOGI(TAG, "Parsing Chinese text...");
        if (!esp_tts_parse_chinese(tts_handle_, text.c_str())) {
            ESP_LOGE(TAG, "Failed to parse text: %s", text.c_str());
            return audio_data;
        }
        ESP_LOGI(TAG, "Chinese text parsed successfully");

        // 合成语音数据，使用配置的语音速度
        int len[1] = {0};
        unsigned int speed = 2; // 默认速度为2
#ifdef CONFIG_ESP_TTS_SPEECH_SPEED
        speed = CONFIG_ESP_TTS_SPEECH_SPEED;
#endif

        do {
            short *data = esp_tts_stream_play(tts_handle_, len, speed);
            if (len[0] > 0 && data != nullptr) {
                // 将数据添加到返回的向量中
                size_t start_pos = audio_data.size();
                audio_data.resize(start_pos + len[0]);
                memcpy(audio_data.data() + start_pos, data, len[0] * sizeof(int16_t));
            }
        } while (len[0] > 0);

        ESP_LOGI(TAG, "Synthesized %zu samples for text: %s", audio_data.size(), text.c_str());
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Exception in SynthesizeText: %s", e.what());
        audio_data.clear();
    } catch (...) {
        ESP_LOGE(TAG, "Unknown exception in SynthesizeText");
        audio_data.clear();
    }
#else
    ESP_LOGW(TAG, "ESP-TTS not enabled in config");
#endif

    return audio_data;
}

void LocalTtsProvider::Cleanup() {
#ifdef CONFIG_ESP_TTS_ENABLE
    if (tts_handle_) {
        esp_tts_destroy(tts_handle_);
        tts_handle_ = nullptr;
    }
    
    if (voice_) {
        esp_tts_voice_set_free(voice_);
        voice_ = nullptr;
    }
    
    if (voice_data_) {
        esp_partition_munmap(mmap_);
        voice_data_ = nullptr;
    }
    
    initialized_ = false;
    ESP_LOGI(TAG, "Local TTS resources cleaned up");
#endif
}

std::vector<std::string> LocalTtsProvider::GetSupportedLanguages() {
    std::vector<std::string> languages;
    languages.push_back("zh-CN"); // 中文
    return languages;
}
