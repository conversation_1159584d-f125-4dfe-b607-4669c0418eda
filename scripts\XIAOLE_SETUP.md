# 🎵 Xiaole语音配置说明

项目已成功配置为使用 **<PERSON><PERSON>（小乐）** 语音模型。

## 📋 配置概览

### ✅ 已完成的配置

1. **代码配置**：
   - `local_tts_provider.cc` - 默认使用 `esp_tts_voice_xiaole` 模型
   - `Kconfig.projbuild` - 默认选择 `ESP_TTS_VOICE_XIAOLE`

2. **构建配置**：
   - `main/CMakeLists.txt` - 烧录 `esp_tts_voice_data_xiaole.dat` 文件

3. **脚本配置**：
   - `flash_voice_data.py` - 默认语音类型为 `xiaole`
   - `flash_voice_data.bat` - Windows脚本默认使用xiaole
   - `flash_voice_data.sh` - Linux/macOS脚本默认使用xiaole

## 🎤 Xiaole语音特点

- **音色**：女性音色，音调相对较高
- **风格**：活泼、清脆、亲和力强
- **文件大小**：2.94 MB
- **适用场景**：
  - 智能玩具、儿童产品
  - 教育类应用
  - 娱乐性产品
  - 需要活泼音色的语音助手

## 🚀 使用方法

### 1. 编译并烧录完整固件
```bash
# 编译并烧录（包含语音数据）
idf.py flash monitor
```

### 2. 单独烧录语音数据
```bash
# 使用Python脚本（推荐）
python scripts/flash_voice_data.py

# 使用Windows批处理
scripts\flash_voice_data.bat

# 使用Linux/macOS脚本
./scripts/flash_voice_data.sh
```

### 3. 指定端口烧录
```bash
# Python脚本
python scripts/flash_voice_data.py -p COM3

# Windows批处理
scripts\flash_voice_data.bat COM3

# Linux/macOS脚本
./scripts/flash_voice_data.sh /dev/ttyUSB0
```

## 🔧 配置验证

### 检查语音数据文件
```bash
# 查看文件是否存在
ls -la components/espressif__esp-sr/esp-tts/esp_tts_chinese/esp_tts_voice_data_xiaole.dat

# Windows
dir components\espressif__esp-sr\esp-tts\esp_tts_chinese\esp_tts_voice_data_xiaole.dat
```

### 验证分区配置
确保分区表中包含 `voice_data` 分区：
```csv
voice_data, data, 0x40, 0xD10000, 2M
```

### 检查代码配置
在 `menuconfig` 中确认：
```
Component config → Xiaozhi Assistant Configuration → ESP-TTS Configuration
→ [*] Enable ESP-TTS for local text-to-speech
→ (X) Xiaole (Female Voice)
```

## 🎯 测试TTS功能

编译烧录后，系统启动时会：
1. 初始化Xiaole语音模型
2. 播放系统提示音（如果启用）
3. 在Alert和系统消息中使用TTS

查看日志输出：
```
I (xxx) LocalTtsProvider: Using Xiaole voice model
I (xxx) LocalTtsProvider: Local TTS initialized successfully
```

## 🔄 切换到其他语音

如需切换到其他语音模型：

### 切换到xiaoxin_small
```bash
# 修改默认语音
python scripts/flash_voice_data.py -v xiaoxin_small

# 或在menuconfig中选择Template Voice并烧录xiaoxin_small数据
```

### 切换到xiaoxin完整版
```bash
python scripts/flash_voice_data.py -v xiaoxin
```

## ⚠️ 注意事项

1. **语音数据与模型匹配**：
   - Xiaole语音数据必须配合 `esp_tts_voice_xiaole` 模型使用
   - 不要混用不同的语音数据和模型

2. **分区大小**：
   - 确保 `voice_data` 分区至少有3MB空间
   - Xiaole语音数据约2.94MB

3. **内存要求**：
   - TTS功能需要足够的RAM来加载语音模型
   - 建议使用ESP32-S3等内存较大的芯片

4. **烧录顺序**：
   - 可以先烧录固件，再单独烧录语音数据
   - 也可以使用 `idf.py flash` 一次性烧录所有内容

## 🐛 故障排除

### TTS初始化失败
- 检查语音数据是否正确烧录到voice_data分区
- 确认分区表配置正确
- 查看内存是否足够

### 语音播放异常
- 确认音频编解码器配置正确
- 检查I2S配置和硬件连接
- 验证音频输出设备工作正常

### 编译错误
- 确保ESP-SR组件正确安装
- 检查TTS相关的头文件是否存在
- 运行 `idf.py reconfigure` 重新配置

---

🎉 **配置完成！** 现在您的项目已经配置为使用活泼清脆的Xiaole语音模型。
