# MEMS3526 数字麦克风激活指南

## 概述

MEMS3526 (LMD3526T261-OAC03) 是一个高性能数字MEMS麦克风，适用于语音识别和音频捕获应用。

## 产品特性

- **型号**: LMD3526T261-OAC03
- **制造商**: LinkMems
- **封装**: SMD-6P, 3.5x2.7mm
- **拾音模式**: 全向 (Omni-directional)
- **灵敏度**: -26dB
- **接口**: PDM数字音频输出 (默认)，也支持I2S模式
- **特点**: 高信噪比、低功耗、抗RF干扰

## 激活步骤

### 1. 启用编译选项

在 `menuconfig` 中启用：

```
Component config → Audio Configuration → 使用 MEMS3526 数字麦克风芯片
```

或在 `sdkconfig` 中添加：

```
CONFIG_USE_MEMS3526_MICROPHONE=y
```

### 2. 代码配置

#### PDM模式 (默认推荐)

PDM (脉冲密度调制) 模式是MEMS3526的默认工作模式，具有以下优势：
- 只需要2个GPIO引脚 (CLK + DATA)
- 硬件连接简单
- 抗干扰能力强
- 适合大多数应用场景

```cpp
#include "mems3526_microphone.h"

// 定义 GPIO 引脚
#define MIC_CLK_PIN     GPIO_NUM_5
#define MIC_DATA_PIN    GPIO_NUM_6

// 创建 MEMS3526 麦克风实例 (PDM模式)
Mems3526Microphone microphone(
    16000,              // 采样率
    MIC_CLK_PIN,        // 时钟引脚
    MIC_DATA_PIN        // 数据引脚
);

// 在 board 文件中使用
virtual AudioCodec* GetAudioCodec() override {
    return &microphone;
}
```

#### I2S模式

```cpp
#include "mems3526_microphone.h"

// 定义 GPIO 引脚
#define MIC_BCLK_PIN    GPIO_NUM_5
#define MIC_WS_PIN      GPIO_NUM_4
#define MIC_DATA_PIN    GPIO_NUM_6

// 创建 MEMS3526 麦克风实例 (I2S模式)
Mems3526Microphone microphone(
    16000,                  // 采样率
    MIC_BCLK_PIN,          // BCLK引脚
    MIC_WS_PIN,            // WS/LR引脚
    MIC_DATA_PIN,          // 数据引脚
    I2S_STD_SLOT_LEFT      // 声道掩码
);
```

### 3. 硬件连接

#### PDM模式连接 (推荐)
```
ESP32S3    MEMS3526
GPIO5  →   CLK
GPIO6  →   DATA
VCC    →   VDD (1.8V-3.6V)
GND    →   GND
```

#### I2S模式连接
```
ESP32S3    MEMS3526
GPIO5  →   BCLK
GPIO4  →   WS/LR
GPIO6  →   DATA
VCC    →   VDD (1.8V-3.6V)
GND    →   GND
```

## 使用方法

### 基本使用

```cpp
// 启动麦克风
microphone.Start();

// 读取音频数据
int16_t audio_buffer[960];  // 60ms @ 16kHz
int samples_read = microphone.Read(audio_buffer, 960);

// 检查状态
microphone.PrintStatus();
```

### 与现有音频系统集成

MEMS3526可以与NS4168音频功放组合使用：

```cpp
// 组合使用示例
class MyAudioBoard : public Board {
private:
    Ns4168AudioCodec speaker_;      // 扬声器
    Mems3526Microphone microphone_; // 麦克风
    
public:
    virtual AudioCodec* GetAudioCodec() override {
        // 根据需要返回相应的音频设备
        // 或创建组合音频编解码器
        return &speaker_;  // 或 &microphone_
    }
};
```

## 技术规格

| 参数 | 规格 |
|------|------|
| 采样率 | 8kHz - 48kHz |
| 数据位宽 | 16-bit |
| 接口模式 | PDM/I2S |
| 功耗 | 低功耗设计 |
| 工作电压 | 1.8V - 3.6V |
| 温度范围 | -40°C ~ +85°C |

## 调试信息

启用后，系统会输出详细的调试信息：

```
I (xxx) Mems3526Microphone: MEMS3526 Microphone constructor started (PDM mode)
I (xxx) Mems3526Microphone: Parameters: sample_rate=16000, clk_pin=5, data_pin=6
I (xxx) Mems3526Microphone: GPIO configuration completed
I (xxx) Mems3526Microphone: PDM mode initialization completed successfully
I (xxx) Mems3526Microphone: MEMS3526 Microphone started successfully
```

## 注意事项

1. **接口选择**: PDM模式更简单，推荐使用
2. **采样率**: 建议使用16kHz，适合语音识别
3. **电源**: 确保提供稳定的电源电压
4. **布线**: 保持时钟和数据线路简短，避免干扰
5. **兼容性**: 需要ESP32S3芯片支持PDM模式

## 故障排除

### 常见问题

1. **无音频输入**: 检查GPIO配置和硬件连接
2. **噪音过大**: 检查电源质量和PCB布线
3. **初始化失败**: 确认芯片支持PDM模式
4. **采样率错误**: 检查时钟配置

### 调试命令

```cpp
// 打印详细状态
microphone.PrintStatus();

// 检查初始化状态
if (!microphone.IsInitialized()) {
    ESP_LOGE(TAG, "Microphone initialization failed");
}
```
