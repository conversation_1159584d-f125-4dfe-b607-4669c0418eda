翻译器，网络搜索，音乐播放，定时器，声音克隆


小智 AI 聊天机器人百科全书
https://ccnphfhqs21z.feishu.cn/wiki/F5krwD16viZoF0kKkvDcrZNYnhb
https://ccnphfhqs21z.feishu.cn/wiki/W14Kw1s1uieoKjkP8N0c1VVvn8d

https://github.com/espressif

串口：https://dev.16302.com/tools/terminal

===================================================================
GPIO编号	功能模块	信号名称	方向	描述
GPIO 0	按键控制	BOOT_BUTTON	输入	启动按键/聊天切换
GPIO 4	音频麦克风	MIC_WS	输出	麦克风字时钟 (Word Select)
GPIO 5	音频麦克风	MIC_SCK	输出	麦克风串行时钟 (Serial Clock)
GPIO 6	音频麦克风	MIC_DIN	输入	麦克风数据输入 (Data Input)
GPIO 7	音频功放	SPK_DOUT	输出	功放数据输出 (Data Output)
GPIO 15	音频功放	SPK_BCLK	输出	功放位时钟 (Bit Clock)
GPIO 16	音频功放	SPK_LRCK	输出	功放左右声道时钟 (LR Clock)
GPIO 18	物联网控制	LAMP_CTRL	输出	灯具控制 (MCP测试)
GPIO 39	按键控制	VOL_DOWN	输入	音量减少按键
GPIO 40	按键控制	VOL_UP	输入	音量增加按键
GPIO 41	显示屏	DISPLAY_SDA	双向	I2C数据线 (OLED)
GPIO 42	显示屏	DISPLAY_SCL	输出	I2C时钟线 (OLED)
GPIO 47	按键控制	TOUCH_BTN	输入	触摸按键 (按住说话)
GPIO 48	LED指示	BUILTIN_LED	输出	内置LED指示灯

采样率: 16kHz
接口: I2S数字麦克风
GPIO	信号	功能描述
GPIO 4	WS (Word Select)	字时钟，控制左右声道切换
GPIO 5	SCK (Serial Clock)	串行位时钟，数据同步时钟
GPIO 6	DIN (Data Input)	数字音频数据输入

采样率: 24kHz
接口: I2S数字功放 (如MAX98357A)
GPIO	信号	功能描述
GPIO 7	DOUT (Data Output)	数字音频数据输出
GPIO 15	BCLK (Bit Clock)	位时钟，数据传输时钟
GPIO 16	LRCK (LR Clock)	左右声道时钟，帧同步

1. 按键控制
GPIO	按键名称	触发方式	功能描述
GPIO 0	BOOT按键	点击	聊天状态切换/WiFi重置
GPIO 47	触摸按键	按住/释放	按住说话模式
GPIO 40	音量+	点击/长按	+10音量/最大音量
GPIO 39	音量-	点击/长按	-10音量/静音

支持型号: SSD1306/SH1106
分辨率: 128x32 或 128x64
I2C地址: 0x3C

GPIO	设备类型	功能描述
GPIO 18	灯具控制	MCP协议测试用灯具开关

I2S麦克风模块 (如INMP441):
- VCC → 3.3V
- GND → GND  
- WS  → GPIO 4
- SCK → GPIO 5
- SD  → GPIO 6

I2S功放模块 (如MAX98357A):
- VCC → 5V
- GND → GND
- BCLK → GPIO 15
- LRCK → GPIO 16  
- DIN  → GPIO 7











