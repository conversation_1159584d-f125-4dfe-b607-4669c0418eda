#ifndef LOCAL_TTS_PROVIDER_H
#define LOCAL_TTS_PROVIDER_H

#include <string>
#include <vector>
#include <memory>

#ifdef CONFIG_ESP_TTS_ENABLE
#include "esp_tts.h"
#include "esp_tts_voice.h"
#include "esp_tts_voice_xiaole.h"
#include "esp_tts_voice_template.h"
#include "esp_partition.h"
#include "spi_flash_mmap.h"
#endif

/**
 * 本地TTS提供者
 * 使用ESP-TTS进行离线语音合成，主要用于Alert和系统提示
 */
class LocalTtsProvider {
private:
#ifdef CONFIG_ESP_TTS_ENABLE
    esp_tts_handle_t tts_handle_;
    esp_tts_voice_t *voice_;
    spi_flash_mmap_handle_t mmap_;
    uint16_t* voice_data_;
    bool initialized_;
#endif

public:
    LocalTtsProvider();
    ~LocalTtsProvider();

    /**
     * 初始化本地TTS系统
     * @return true if successful, false otherwise
     */
    bool Initialize();

    /**
     * 检查本地TTS是否可用
     * @return true if available, false otherwise
     */
    bool IsAvailable() const;

    /**
     * 合成文本为音频数据
     * @param text 要合成的中文文本
     * @return 音频数据（16位PCM，16kHz采样率）
     */
    std::vector<int16_t> SynthesizeText(const std::string& text);

    /**
     * 清理资源
     */
    void Cleanup();

    /**
     * 获取支持的语言列表
     */
    static std::vector<std::string> GetSupportedLanguages();
};

#endif // LOCAL_TTS_PROVIDER_H
