#!/usr/bin/env python3
"""
ESP32 TTS语音数据烧录脚本

此脚本用于单独烧录TTS语音数据到ESP32设备的voice_data分区。
支持多种语音数据文件和自动检测目标芯片类型。

使用方法:
    python scripts/flash_voice_data.py [选项]

选项:
    -p, --port PORT         串口端口 (默认: 自动检测)
    -b, --baud BAUD         波特率 (默认: 460800)
    -v, --voice VOICE       语音类型 (xiaoxin_small|xiaoxin|xiaole|xiaoxin_custom)
    -t, --target TARGET     目标芯片 (esp32|esp32s2|esp32s3|esp32c3|esp32c6|esp32c5|esp32p4)
    --partition-table FILE  分区表文件路径
    --dry-run              仅显示将要执行的命令，不实际烧录
    -h, --help             显示帮助信息

示例:
    python scripts/flash_voice_data.py
    python scripts/flash_voice_data.py -p COM3 -v xiaoxin_small
    python scripts/flash_voice_data.py -t esp32s3 -v xiaole --dry-run
"""

import os
import sys
import argparse
import subprocess
import json
from pathlib import Path

# 脚本所在目录
SCRIPT_DIR = Path(__file__).parent
PROJECT_ROOT = SCRIPT_DIR.parent

# 默认配置
DEFAULT_BAUD = 460800
DEFAULT_VOICE = "xiaoxin_small"
DEFAULT_TARGET = "esp32s3"

# 语音数据文件映射
VOICE_DATA_FILES = {
    "xiaoxin_small": "esp_tts_voice_data_xiaoxin_small.dat",
    "xiaoxin": "esp_tts_voice_data_xiaoxin.dat", 
    "xiaole": "esp_tts_voice_data_xiaole.dat",
    "xiaoxin_custom": "esp_tts_voice_data_xiaoxin_custom.dat"
}

# 支持的目标芯片
SUPPORTED_TARGETS = ["esp32", "esp32s2", "esp32s3", "esp32c3", "esp32c6", "esp32c5", "esp32p4"]

def find_esp_sr_path():
    """查找ESP-SR组件路径"""
    # 首先检查components目录
    components_path = PROJECT_ROOT / "components" / "espressif__esp-sr"
    if components_path.exists():
        return components_path
    
    # 然后检查managed_components目录
    managed_path = PROJECT_ROOT / "managed_components" / "espressif__esp-sr"
    if managed_path.exists():
        return managed_path
    
    return None

def get_voice_data_path(voice_type, target):
    """获取语音数据文件路径"""
    esp_sr_path = find_esp_sr_path()
    if not esp_sr_path:
        raise FileNotFoundError("未找到ESP-SR组件路径")
    
    voice_file = VOICE_DATA_FILES.get(voice_type)
    if not voice_file:
        raise ValueError(f"不支持的语音类型: {voice_type}")
    
    voice_data_path = esp_sr_path / "esp-tts" / "esp_tts_chinese" / voice_file
    
    if not voice_data_path.exists():
        raise FileNotFoundError(f"语音数据文件不存在: {voice_data_path}")
    
    return voice_data_path

def parse_partition_table(partition_file):
    """解析分区表文件，获取voice_data分区信息"""
    if not partition_file.exists():
        raise FileNotFoundError(f"分区表文件不存在: {partition_file}")
    
    with open(partition_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if line.startswith('#') or not line:
            continue
        
        parts = [part.strip() for part in line.split(',')]
        if len(parts) >= 4 and parts[0] == 'voice_data':
            # voice_data, data, 0x40, offset, size
            name, type_field, subtype, offset, size = parts[:5]
            return {
                'name': name,
                'offset': offset,
                'size': size
            }
    
    raise ValueError("在分区表中未找到voice_data分区")

def find_partition_table():
    """自动查找分区表文件"""
    # 检查sdkconfig中的分区表配置
    sdkconfig_path = PROJECT_ROOT / "sdkconfig"
    if sdkconfig_path.exists():
        with open(sdkconfig_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('CONFIG_PARTITION_TABLE_CUSTOM_FILENAME='):
                    partition_file = line.split('=')[1].strip().strip('"')
                    partition_path = PROJECT_ROOT / partition_file
                    if partition_path.exists():
                        return partition_path
    
    # 默认查找常用的分区表文件
    default_partitions = [
        "partitions/v1/16m.csv",
        "partitions/v1/8m.csv", 
        "partitions/v1/32m.csv",
        "partitions/v1/4m.csv"
    ]
    
    for partition_file in default_partitions:
        partition_path = PROJECT_ROOT / partition_file
        if partition_path.exists():
            return partition_path
    
    raise FileNotFoundError("未找到分区表文件")

def detect_target():
    """自动检测目标芯片类型"""
    try:
        # 尝试从sdkconfig读取
        sdkconfig_path = PROJECT_ROOT / "sdkconfig"
        if sdkconfig_path.exists():
            with open(sdkconfig_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.startswith('CONFIG_IDF_TARGET='):
                        target = line.split('=')[1].strip().strip('"')
                        if target in SUPPORTED_TARGETS:
                            return target
        
        # 默认返回esp32s3
        return DEFAULT_TARGET
    except Exception:
        return DEFAULT_TARGET

def run_esptool_command(args, dry_run=False):
    """执行esptool命令"""
    cmd = ["python", "-m", "esptool"] + args
    
    print(f"执行命令: {' '.join(cmd)}")
    
    if dry_run:
        print("(干运行模式，不实际执行)")
        return True
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("命令执行成功!")
        if result.stdout:
            print("输出:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print("错误:", e.stderr)
        return False

def main():
    parser = argparse.ArgumentParser(
        description="ESP32 TTS语音数据烧录脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("-p", "--port", help="串口端口")
    parser.add_argument("-b", "--baud", type=int, default=DEFAULT_BAUD, help=f"波特率 (默认: {DEFAULT_BAUD})")
    parser.add_argument("-v", "--voice", choices=VOICE_DATA_FILES.keys(), default=DEFAULT_VOICE, 
                       help=f"语音类型 (默认: {DEFAULT_VOICE})")
    parser.add_argument("-t", "--target", choices=SUPPORTED_TARGETS, help="目标芯片类型")
    parser.add_argument("--partition-table", type=Path, help="分区表文件路径")
    parser.add_argument("--dry-run", action="store_true", help="仅显示将要执行的命令，不实际烧录")
    
    args = parser.parse_args()
    
    try:
        # 检测目标芯片
        target = args.target or detect_target()
        print(f"目标芯片: {target}")
        
        # 获取语音数据文件路径
        voice_data_path = get_voice_data_path(args.voice, target)
        print(f"语音数据文件: {voice_data_path}")
        print(f"文件大小: {voice_data_path.stat().st_size / 1024 / 1024:.2f} MB")
        
        # 获取分区表信息
        partition_table = args.partition_table or find_partition_table()
        print(f"分区表文件: {partition_table}")
        
        partition_info = parse_partition_table(partition_table)
        print(f"voice_data分区: offset={partition_info['offset']}, size={partition_info['size']}")
        
        # 构建esptool命令
        esptool_args = []
        
        if args.port:
            esptool_args.extend(["--port", args.port])
        
        esptool_args.extend([
            "--baud", str(args.baud),
            "write_flash",
            partition_info['offset'],
            str(voice_data_path)
        ])
        
        # 执行烧录命令
        print("\n开始烧录语音数据...")
        success = run_esptool_command(esptool_args, args.dry_run)
        
        if success and not args.dry_run:
            print("\n✅ 语音数据烧录完成!")
            print("现在可以重启设备测试TTS功能。")
        elif args.dry_run:
            print("\n✅ 干运行完成，命令验证通过。")
        else:
            print("\n❌ 语音数据烧录失败!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
