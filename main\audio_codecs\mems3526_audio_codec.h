#ifndef _MEMS3526_AUDIO_CODEC_H
#define _MEMS3526_AUDIO_CODEC_H

#include "audio_codec.h"

#include <driver/gpio.h>
#include <driver/i2s_pdm.h>

/**
 * MEMS3526 PDM麦克风音频编解码器
 * 仅支持音频输入，使用PDM接口
 * 适用于只需要麦克风输入的应用场景
 */
class Mems3526AudioCodec : public AudioCodec {
private:
    bool use_pdm_mode_;  // true=PDM模式, false=I2S模式

    void CreatePdmInputChannel(gpio_num_t clk, gpio_num_t din);
    void CreateI2sInputChannel(gpio_num_t ws, gpio_num_t clk, gpio_num_t din);

    virtual int Read(int16_t* dest, int samples) override;
    virtual int Write(const int16_t* data, int samples) override;

public:
    // PDM模式构造函数（保持兼容性）
    Mems3526AudioCodec(int input_sample_rate, gpio_num_t mic_clk, gpio_num_t mic_din);
    // I2S模式构造函数
    Mems3526AudioCodec(int input_sample_rate, gpio_num_t mic_ws, gpio_num_t mic_clk, gpio_num_t mic_din);
    virtual ~Mems3526AudioCodec();

    virtual void Start() override;
    virtual void SetOutputVolume(int volume) override;
    virtual void EnableInput(bool enable) override;
    virtual void EnableOutput(bool enable) override;
};

#endif // _MEMS3526_AUDIO_CODEC_H
