#include "mems3526_audio_codec.h"

#include <esp_log.h>
#include <cstring>
#include "settings.h"

#define TAG "Mems3526AudioCodec"

// PDM模式构造函数
Mems3526AudioCodec::Mems3526AudioCodec(int input_sample_rate, gpio_num_t mic_clk, gpio_num_t mic_din) {

    ESP_LOGI(TAG, "MEMS3526 Microphone constructor started (PDM mode)");
    ESP_LOGI(TAG, "Parameters: sample_rate=%d, clk_pin=%d, data_pin=%d", input_sample_rate, (int)mic_clk, (int)mic_din);

    duplex_ = false; // 仅支持输入
    input_reference_ = false;
    input_channels_ = 1;
    input_sample_rate_ = input_sample_rate;
    output_sample_rate_ = 0; // 不支持输出
    use_pdm_mode_ = true;

    ESP_LOGI(TAG, "Configuring GPIO pins");

    // 创建 PDM 输入通道 (MEMS3526 麦克风)
    CreatePdmInputChannel(mic_clk, mic_din);

    ESP_LOGI(TAG, "MEMS3526 Microphone constructor completed (PDM mode)");
}

// I2S模式构造函数
Mems3526AudioCodec::Mems3526AudioCodec(int input_sample_rate, gpio_num_t mic_ws, gpio_num_t mic_clk, gpio_num_t mic_din) {

    ESP_LOGI(TAG, "MEMS3526 Microphone constructor started (I2S mode)");
    ESP_LOGI(TAG, "Parameters: sample_rate=%d, ws_pin=%d, clk_pin=%d, data_pin=%d",
             input_sample_rate, (int)mic_ws, (int)mic_clk, (int)mic_din);

    duplex_ = false; // 仅支持输入
    input_reference_ = false;
    input_channels_ = 1;
    input_sample_rate_ = input_sample_rate;
    output_sample_rate_ = 0; // 不支持输出
    use_pdm_mode_ = false;

    ESP_LOGI(TAG, "Configuring GPIO pins");

    // 创建 I2S 输入通道 (MEMS3526 麦克风)
    CreateI2sInputChannel(mic_ws, mic_clk, mic_din);

    ESP_LOGI(TAG, "MEMS3526 Microphone constructor completed (I2S mode)");
}

Mems3526AudioCodec::~Mems3526AudioCodec() {
    if (rx_handle_) {
        i2s_channel_disable(rx_handle_);
        i2s_del_channel(rx_handle_);
    }
}

void Mems3526AudioCodec::CreatePdmInputChannel(gpio_num_t clk, gpio_num_t din) {
    ESP_LOGI(TAG, "Initializing PDM mode");
    ESP_LOGI(TAG, "PDM configuration: CLK=GPIO%d, DATA=GPIO%d, sample_rate=%d", (int)clk, (int)din, input_sample_rate_);

    // PDM 输入通道配置 (MEMS3526 麦克风) - 必须使用I2S0，因为PDM只在I2S0上支持
    i2s_chan_config_t chan_cfg = I2S_CHANNEL_DEFAULT_CONFIG(I2S_NUM_0, I2S_ROLE_MASTER);
    chan_cfg.dma_desc_num = AUDIO_CODEC_DMA_DESC_NUM;
    chan_cfg.dma_frame_num = AUDIO_CODEC_DMA_FRAME_NUM;

    ESP_LOGI(TAG, "Creating I2S channel with %u descriptors, %u frames each",
             (unsigned int)chan_cfg.dma_desc_num, (unsigned int)chan_cfg.dma_frame_num);

    esp_err_t ret = i2s_new_channel(&chan_cfg, nullptr, &rx_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create I2S RX channel: %s", esp_err_to_name(ret));
        return;
    }
    ESP_LOGI(TAG, "I2S RX channel created successfully, handle: %p", rx_handle_);

    // 配置PDM接收模式 - 优化时钟配置减少杂音（参考代码优化）
    i2s_pdm_rx_config_t pdm_rx_cfg = {
        .clk_cfg = {
            .sample_rate_hz = (uint32_t)input_sample_rate_,
            .clk_src = I2S_CLK_SRC_DEFAULT,  // 与NS4168使用相同时钟源
            .mclk_multiple = I2S_MCLK_MULTIPLE_256,  // 与NS4168保持一致
        },
        .slot_cfg = I2S_PDM_RX_SLOT_DEFAULT_CONFIG(I2S_DATA_BIT_WIDTH_16BIT, I2S_SLOT_MODE_MONO),
        .gpio_cfg = {
            .clk = clk,
            .din = din,
            .invert_flags = {
                .clk_inv = false,
            },
        },
    };

    ESP_LOGI(TAG, "Initializing PDM RX mode...");
    ret = i2s_channel_init_pdm_rx_mode(rx_handle_, &pdm_rx_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize PDM RX mode: %s", esp_err_to_name(ret));
        return;
    }

    ESP_LOGI(TAG, "GPIO configuration completed");
    ESP_LOGI(TAG, "PDM mode initialization completed successfully");
    ESP_LOGI(TAG, "MEMS3526 PDM input channel created on I2S0");
}

void Mems3526AudioCodec::CreateI2sInputChannel(gpio_num_t ws, gpio_num_t clk, gpio_num_t din) {
    ESP_LOGI(TAG, "Initializing I2S mode");
    ESP_LOGI(TAG, "I2S configuration: WS=GPIO%d, CLK=GPIO%d, DATA=GPIO%d, sample_rate=%d",
             (int)ws, (int)clk, (int)din, input_sample_rate_);

    // I2S 输入通道配置 (MEMS3526 麦克风) - 使用I2S0
    i2s_chan_config_t chan_cfg = I2S_CHANNEL_DEFAULT_CONFIG(I2S_NUM_0, I2S_ROLE_MASTER);
    chan_cfg.dma_desc_num = AUDIO_CODEC_DMA_DESC_NUM;
    chan_cfg.dma_frame_num = AUDIO_CODEC_DMA_FRAME_NUM;

    ESP_LOGI(TAG, "Creating I2S channel with %u descriptors, %u frames each",
             (unsigned int)chan_cfg.dma_desc_num, (unsigned int)chan_cfg.dma_frame_num);

    esp_err_t ret = i2s_new_channel(&chan_cfg, nullptr, &rx_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create I2S RX channel: %s", esp_err_to_name(ret));
        return;
    }
    ESP_LOGI(TAG, "I2S RX channel created successfully, handle: %p", rx_handle_);

    i2s_std_config_t std_cfg = {
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(static_cast<uint32_t>(input_sample_rate_)),
        .slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(I2S_DATA_BIT_WIDTH_16BIT, I2S_SLOT_MODE_MONO),
        .gpio_cfg = {
            .mclk = I2S_GPIO_UNUSED,
            .bclk = clk,
            .ws = ws,
            .dout = I2S_GPIO_UNUSED,
            .din = din,
            .invert_flags = {
                .mclk_inv = false,
                .bclk_inv = false,
                .ws_inv = false,
            },
        },
    };

    ESP_LOGI(TAG, "Initializing I2S STD mode...");
    ret = i2s_channel_init_std_mode(rx_handle_, &std_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize I2S STD mode: %s", esp_err_to_name(ret));
        return;
    }

    ESP_LOGI(TAG, "GPIO configuration completed");
    ESP_LOGI(TAG, "I2S mode initialization completed successfully");
    ESP_LOGI(TAG, "MEMS3526 I2S input channel created on I2S0");
}

int Mems3526AudioCodec::Write(const int16_t* data, int samples) {
    // MEMS3526仅支持输入，不支持输出
    ESP_LOGW(TAG, "Write operation not supported - MEMS3526 is input-only");
    return 0;
}

int Mems3526AudioCodec::Read(int16_t* dest, int samples) {
    static int debug_count = 0;
    static int total_reads = 0;

    total_reads++;

    if (!rx_handle_) {
        if (debug_count < 3) {
            ESP_LOGW(TAG, "Read failed: rx_handle is NULL");
            debug_count++;
        }
        return 0;
    }

    if (!input_enabled_) {
        if (debug_count < 3) {
            ESP_LOGW(TAG, "Read failed: input not enabled");
            debug_count++;
        }
        return 0;
    }

    size_t bytes_read;
    // PDM模式：使用portMAX_DELAY等待数据（参考代码实现）
    esp_err_t ret = i2s_channel_read(rx_handle_, dest, samples * sizeof(int16_t), &bytes_read, portMAX_DELAY);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "MEMS3526 PDM Read failed: %s", esp_err_to_name(ret));
        memset(dest, 0, samples * sizeof(int16_t));
        return samples;
    }

    int actual_samples = bytes_read / sizeof(int16_t);

    static int read_count = 0;
    if (read_count < 5) {  // 只打印前5次
        // 检查音频数据范围
        int16_t max_amplitude = 0;
        int16_t min_amplitude = 0;
        for (int i = 0; i < actual_samples; i++) {
            if (dest[i] > max_amplitude) max_amplitude = dest[i];
            if (dest[i] < min_amplitude) min_amplitude = dest[i];
        }
        ESP_LOGI(TAG, "Read %d samples from MEMS3526 (requested %d), amplitude range: %d to %d",
                 actual_samples, samples, min_amplitude, max_amplitude);
        read_count++;
    }

    return actual_samples;
}

void Mems3526AudioCodec::Start() {
    ESP_LOGI(TAG, "Starting MEMS3526 Microphone...");

    // 启用I2S通道
    if (rx_handle_) {
        ESP_LOGI(TAG, "Enabling I2S RX channel...");
        esp_err_t ret = i2s_channel_enable(rx_handle_);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to enable I2S RX channel: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "I2S RX channel enabled successfully");
        }
    } else {
        ESP_LOGW(TAG, "RX handle is NULL, cannot enable input");
    }

    EnableInput(true);

    // 验证输入是否真的启用了
    ESP_LOGI(TAG, "Input enabled status: %s", input_enabled_ ? "true" : "false");
    ESP_LOGI(TAG, "RX handle: %p", rx_handle_);

    // 等待一段时间让I2S稳定
    vTaskDelay(pdMS_TO_TICKS(100));

    // 测试读取一些数据
    std::vector<int16_t> test_buffer(160);  // 10ms @ 16kHz
    int test_samples = Read(test_buffer.data(), 160);
    ESP_LOGI(TAG, "Test read result: %d samples", test_samples);

    // 不启用输出，因为MEMS3526仅支持输入
    ESP_LOGI(TAG, "MEMS3526 Microphone started successfully");
}

void Mems3526AudioCodec::SetOutputVolume(int volume) {
    // MEMS3526仅支持输入，不支持音量控制
    ESP_LOGW(TAG, "Volume control not supported - MEMS3526 is input-only");
}

void Mems3526AudioCodec::EnableInput(bool enable) {
    input_enabled_ = enable;
    ESP_LOGI(TAG, "MEMS3526 input %s", enable ? "enabled" : "disabled");
}

void Mems3526AudioCodec::EnableOutput(bool enable) {
    // MEMS3526仅支持输入，不支持输出
    if (enable) {
        ESP_LOGW(TAG, "Output enable not supported - MEMS3526 is input-only");
    }
}
