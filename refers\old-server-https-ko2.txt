ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2820,len:0x56c
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc2c
load:0x403cb700,len:0x2e58
entry 0x403c890c
I (87) octal_psram: vendor id    : 0x0d (AP)
I (87) octal_psram: dev id       : 0x02 (generation 3)
I (87) octal_psram: density      : 0x03 (64 Mbit)
I (89) octal_psram: good-die     : 0x01 (Pass)
I (93) octal_psram: Latency      : 0x01 (Fixed)
I (98) octal_psram: VCC          : 0x01 (3V)
I (102) octal_psram: SRF          : 0x01 (Fast Refresh)
I (107) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (111) octal_psram: BurstLen     : 0x01 (32 Byte)
I (116) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (121) octal_psram: DriveStrength: 0x00 (1/1)
I (126) MSPI Timing: PSRAM timing tuning index: 4
I (130) esp_psram: Found 8MB PSRAM device
I (134) esp_psram: Speed: 80MHz
I (136) cpu_start: Multicore app
I (151) cpu_start: Pro cpu start user code
I (151) cpu_start: cpu freq: 240000000 Hz
I (151) app_init: Application information:
I (151) app_init: Project name:     xiaozhi
I (155) app_init: App version:      1.7.6
I (159) app_init: Compile time:     Jun 29 2025 18:48:58
I (164) app_init: ELF file SHA256:  a69537411...
I (168) app_init: ESP-IDF:          v5.4.1-dirty
I (173) efuse_init: Min chip rev:     v0.0
I (176) efuse_init: Max chip rev:     v0.99 
I (180) efuse_init: Chip rev:         v0.2
I (184) heap_init: Initializing. RAM available for dynamic allocation:
I (191) heap_init: At 3FCAD350 len 0003C3C0 (240 KiB): RAM
I (196) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (201) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (206) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (213) spi_flash: detected chip: generic
I (216) spi_flash: flash io: qio
I (220) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (226) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (233) main_task: Started on CPU0
I (263) esp_psram: Reserving pool of 64K of internal memory for DMA/internal allocations
I (263) main_task: Calling app_main()
I (263) BackgroundTask: background_task started
I (273) Board: UUID=e60d182b-aad7-4e96-984a-91145b91843c SKU=bread-compact-wifi
I (273) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (283) button: IoT Button Version: 4.1.3
I (283) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (293) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (303) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (313) CompactWifiBoard: Install SSD1306 driver
I (313) CompactWifiBoard: SSD1306 driver installed
I (323) CompactWifiBoard: Turning display on
I (423) Display: Power management not supported
I (423) OledDisplay: Initialize LVGL
I (423) LVGL: Starting LVGL task
I (423) OledDisplay: Adding OLED display
I (443) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (443) MCP: Add tool: self.lamp.get_state
I (443) MCP: Add tool: self.lamp.turn_on
I (453) MCP: Add tool: self.lamp.turn_off
I (453) Application: STATE: starting
I (453) NoAudioCodec: Simplex channels created
I (463) Application: Audio processor detected, setting opus encoder complexity to 5
I (463) AudioCodec: Set input enable to true
I (473) AudioCodec: Set output enable to true
I (473) AudioCodec: Audio codec started
I (483) Application: STATE: configuring
I (483) DnsServer: Starting DNS server
I (483) pp: pp rom version: e7ae62f
I (493) net80211: net80211 rom version: e7ae62f
I (503) wifi:wifi driver task: 3fcdc424, prio:23, stack:6144, core=0
I (503) wifi:wifi firmware version: 79fa3f41ba
I (503) wifi:wifi certification version: v7.0
I (503) wifi:config NVS flash: enabled
I (513) wifi:config nano formatting: enabled
I (513) wifi:Init data frame dynamic rx buffer num: 8
I (523) wifi:Init dynamic rx mgmt buffer num: 5
I (523) wifi:Init management short buffer num: 32
I (533) wifi:Init dynamic tx buffer num: 32
I (533) wifi:Init static tx FG buffer num: 2
I (533) wifi:Init static rx buffer size: 1600
I (543) wifi:Init static rx buffer num: 6
I (543) wifi:Init dynamic rx buffer num: 8
I (553) wifi_init: rx ba win: 6
I (553) wifi_init: accept mbox: 6
I (553) wifi_init: tcpip mbox: 32
I (553) wifi_init: udp mbox: 6
I (563) wifi_init: tcp mbox: 6
I (563) wifi_init: tcp tx win: 5760
I (563) wifi_init: tcp rx win: 5760
I (573) wifi_init: tcp mss: 1440
I (583) wifi:Set ps type: 0, coexist: 0

I (583) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
W (583) phy_init: failed to load RF calibration data (0x1102), falling back to full calibration
I (613) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(2)
I (633) wifi:mode : sta (b4:3a:45:a1:e4:64) + softAP (b4:3a:45:a1:e4:65)
I (633) wifi:enable tsf
I (633) wifi:Total power save buffer number: 16
I (633) wifi:Init max length of beacon: 752/752
I (633) wifi:Init max length of beacon: 752/752
I (643) WifiConfigurationAp: Access Point started with SSID Xiaozhi-E465
I (643) esp_netif_lwip: DHCP server started on interface WIFI_AP_DEF with IP: ***********
I (653) WifiConfigurationAp: Web server started
W (663) Application: Alert 配网模式: 手机连接热点 Xiaozhi-E465，浏览器访问 http://***********

 []
I (683) WifiBoard: Free internal: 120687 minimal internal: 120687
I (693) Application: Resampling audio from 16000 to 24000
I (693) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (10493) SystemInfo: free sram: 125115 minimal sram: 120055
I (10693) WifiBoard: Free internal: 125035 minimal internal: 120055
I (18633) wifi:new:<1,0>, old:<1,1>, ap:<1,0>, sta:<0,0>, prof:1, snd_ch_cfg:0x0
I (18633) wifi:station: ee:20:c6:13:db:10 join, AID=1, bgn, 20
I (18633) WifiConfigurationAp: Station ee:20:c6:13:db:10 joined, AID=1
I (19063) esp_netif_lwip: DHCP server assigned IP to a client, IP is: ***********
I (19363) DnsServer: Sending DNS response to ***********
I (19363) DnsServer: Sending DNS response to ***********
I (20493) SystemInfo: free sram: 124115 minimal sram: 117387
I (20693) WifiBoard: Free internal: 124039 minimal internal: 117387
I (22123) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -21, Authmode: 4
I (22123) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -31, Authmode: 4
I (22123) WifiConfigurationAp: SSID: ASUS2G, RSSI: -46, Authmode: 3
I (22133) WifiConfigurationAp: SSID: Tenda_EB05C0, RSSI: -63, Authmode: 3
I (22143) WifiConfigurationAp: SSID: CU_faUE, RSSI: -72, Authmode: 3
I (22143) WifiConfigurationAp: SSID: TP-LINK_HOME_CICI, RSSI: -83, Authmode: 4
I (22363) wifi:<ba-add>idx:2 (ifx:1, ee:20:c6:13:db:10), tid:0, ssn:122, winSize:64
I (24463) DnsServer: Sending DNS response to ***********
I (24463) DnsServer: Sending DNS response to ***********
I (27553) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -21, Authmode: 4
I (27553) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -31, Authmode: 4
I (27563) WifiConfigurationAp: SSID: ASUS2G, RSSI: -46, Authmode: 3
I (27563) WifiConfigurationAp: SSID: Tenda_EB05C0, RSSI: -63, Authmode: 3
I (27573) WifiConfigurationAp: SSID: CU_faUE, RSSI: -72, Authmode: 3
I (27573) WifiConfigurationAp: SSID: TP-LINK_HOME_CICI, RSSI: -83, Authmode: 4
I (30483) SystemInfo: free sram: 123955 minimal sram: 116535
I (30693) WifiBoard: Free internal: 123955 minimal internal: 116535
W (32653) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (32673) WifiConfigurationAp: Connecting to WiFi TPMLPLINK
I (35113) DnsServer: Sending DNS response to ***********
I (35123) DnsServer: Sending DNS response to ***********
I (35853) wifi:new:<1,1>, old:<1,0>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (35853) wifi:state: init -> auth (0xb0)
I (35863) wifi:state: auth -> assoc (0x0)
I (35873) wifi:state: assoc -> run (0x10)
I (35893) wifi:connected with TPMLPLINK, aid = 2, channel 1, 40U, bssid = f4:84:8d:1a:28:b3
I (35893) wifi:security: WPA2-PSK, phy: bgn, rssi: -21
I (35903) wifi:pm start, type: 0

I (35903) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (35903) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (35913) WifiConfigurationAp: Connected to WiFi TPMLPLINK
I (35923) wifi:state: run -> init (0x0)
I (35933) wifi:pm stop, total sleep time: lu us / lu us

I (35933) wifi:new:<1,0>, old:<1,1>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (35933) WifiConfigurationAp: Save SSID TPMLPLINK 9
I (36123) DnsServer: Sending DNS response to ***********
I (36133) DnsServer: Sending DNS response to ***********
I (36133) DnsServer: Sending DNS response to ***********
I (39063) WifiConfigurationAp: Rebooting...
I (39463) wifi:station: ee:20:c6:13:db:10 leave, AID = 1, reason = 2, bss_flags is 33721443, bss:0x3c29fc48
I (39463) wifi:new:<1,0>, old:<1,0>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (39463) wifi:<ba-del>idx:2, tid:0
I (39473) WifiConfigurationAp: Station ee:20:c6:13:db:10 left, AID=1
I (39573) wifi:flush txq
I (39573) wifi:stop sw txq
I (39573) wifi:lmac stop hw txq
SP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x2b (SPI_FAST_FLASH_BOOT)
Saved PC:0x40380fba
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2820,len:0x56c
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc2c
load:0x403cb700,len:0x2e58
entry 0x403c890c
I (40) octal_psram: vendor id    : 0x0d (AP)
I (40) octal_psram: dev id       : 0x02 (generation 3)
I (41) octal_psram: density      : 0x03 (64 Mbit)
I (42) octal_psram: good-die     : 0x01 (Pass)
I (47) octal_psram: Latency      : 0x01 (Fixed)
I (51) octal_psram: VCC          : 0x01 (3V)
I (55) octal_psram: SRF          : 0x01 (Fast Refresh)
I (60) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (64) octal_psram: BurstLen     : 0x01 (32 Byte)
I (69) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (74) octal_psram: DriveStrength: 0x00 (1/1)
I (79) MSPI Timing: PSRAM timing tuning index: 10
I (82) esp_psram: Found 8MB PSRAM device
I (86) esp_psram: Speed: 80MHz
I (89) cpu_start: Multicore app
I (104) cpu_start: Pro cpu start user code
I (104) cpu_start: cpu freq: 240000000 Hz
I (104) app_init: Application information:
I (104) app_init: Project name:     xiaozhi
I (108) app_init: App version:      1.7.6
I (111) app_init: Compile time:     Jun 29 2025 18:48:58
I (116) app_init: ELF file SHA256:  a69537411...
I (121) app_init: ESP-IDF:          v5.4.1-dirty
I (125) efuse_init: Min chip rev:     v0.0
I (129) efuse_init: Max chip rev:     v0.99 
I (133) efuse_init: Chip rev:         v0.2
I (137) heap_init: Initializing. RAM available for dynamic allocation:
I (143) heap_init: At 3FCAD350 len 0003C3C0 (240 KiB): RAM
I (148) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (153) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (159) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (166) spi_flash: detected chip: generic
I (169) spi_flash: flash io: qio
I (173) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (178) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (185) main_task: Started on CPU0
I (215) esp_psram: Reserving pool of 64K of internal memory for DMA/internal allocations
I (215) main_task: Calling app_main()
I (235) BackgroundTask: background_task started
I (235) Board: UUID=e60d182b-aad7-4e96-984a-91145b91843c SKU=bread-compact-wifi
I (235) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (245) button: IoT Button Version: 4.1.3
I (245) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (255) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (265) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (275) CompactWifiBoard: Install SSD1306 driver
I (275) CompactWifiBoard: SSD1306 driver installed
I (285) CompactWifiBoard: Turning display on
I (385) Display: Power management not supported
I (385) OledDisplay: Initialize LVGL
I (385) LVGL: Starting LVGL task
I (385) OledDisplay: Adding OLED display
I (405) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (405) MCP: Add tool: self.lamp.get_state
I (405) MCP: Add tool: self.lamp.turn_on
I (415) MCP: Add tool: self.lamp.turn_off
I (415) Application: STATE: starting
I (415) NoAudioCodec: Simplex channels created
I (425) Application: Audio processor detected, setting opus encoder complexity to 5
I (425) AudioCodec: Set input enable to true
I (435) AudioCodec: Set output enable to true
I (435) AudioCodec: Audio codec started
I (445) pp: pp rom version: e7ae62f
I (445) net80211: net80211 rom version: e7ae62f
I (455) wifi:wifi driver task: 3fcdb244, prio:23, stack:6144, core=0
I (455) wifi:wifi firmware version: 79fa3f41ba
I (455) wifi:wifi certification version: v7.0
I (465) wifi:config NVS flash: disabled
I (465) wifi:config nano formatting: enabled
I (475) wifi:Init data frame dynamic rx buffer num: 8
I (475) wifi:Init dynamic rx mgmt buffer num: 5
I (475) wifi:Init management short buffer num: 32
I (485) wifi:Init dynamic tx buffer num: 32
I (485) wifi:Init static tx FG buffer num: 2
I (495) wifi:Init static rx buffer size: 1600
I (495) wifi:Init static rx buffer num: 6
I (495) wifi:Init dynamic rx buffer num: 8
I (505) wifi_init: rx ba win: 6
I (505) wifi_init: accept mbox: 6
I (505) wifi_init: tcpip mbox: 32
I (515) wifi_init: udp mbox: 6
I (515) wifi_init: tcp mbox: 6
I (515) wifi_init: tcp tx win: 5760
I (525) wifi_init: tcp rx win: 5760
I (525) wifi_init: tcp mss: 1440
I (525) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
I (565) wifi:mode : sta (b4:3a:45:a1:e4:64)
I (565) wifi:enable tsf
I (2965) wifi: Found AP: TPMLPLINK, BSSID: f4:84:8d:1a:28:b3, RSSI: -23, Channel: 1, Authmode: 4
I (2975) wifi: Found AP: TPMLPLINK, BSSID: 68:77:24:b5:c4:cf, RSSI: -33, Channel: 1, Authmode: 4
W (2975) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (3325) wifi:new:<1,1>, old:<1,0>, ap:<255,255>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (3325) wifi:state: init -> auth (0xb0)
I (3325) wifi:state: auth -> assoc (0x0)
I (3335) wifi:state: assoc -> run (0x10)
I (3345) wifi:connected with TPMLPLINK, aid = 2, channel 1, 40U, bssid = f4:84:8d:1a:28:b3
I (3345) wifi:security: WPA2-PSK, phy: bgn, rssi: -22
I (3345) wifi:pm start, type: 1

I (3345) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (3355) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (3405) wifi:AP's beacon interval = 102400 us, DTIM period = 1
I (3515) wifi:<ba-add>idx:0 (ifx:0, f4:84:8d:1a:28:b3), tid:0, ssn:0, winSize:64
I (4545) wifi: Got IP: *************
I (4545) esp_netif_handlers: sta ip: *************, mask: *************, gw: ***********
I (4555) Application: STATE: activating
I (4565) Ota: Current version: 1.7.6
I (4565) EspHttp: Opening HTTP connection to http://*************:8002/xiaozhi/ota/
I (4705) Ota: No mqtt section found !
I (4715) Ota: Current is the latest version
I (4715) Ota: Running partition: ota_0
I (4715) MCP: Add tool: self.get_device_status
I (4715) MCP: Add tool: self.audio_speaker.set_volume
I (4715) MODEL_LOADER: The storage free size is 21952 KB
I (4725) MODEL_LOADER: The partition size is 960 KB
I (4725) MODEL_LOADER: Successfully load srmodels
I (4735) AFE: AFE Version: (1MIC_V250121)
I (4735) AFE: Input PCM Config: total 1 channels(1 microphone, 0 playback), sample rate:16000
I (4745) AFE: AFE Pipeline: [input] -> |VAD(WebRTC)| -> [output]
I (4745) AfeAudioProcessor: Audio communication task started, feed size: 512 fetch size: 512
I (4755) AfeWakeWord: Model 0: wn9_nihaoxiaozhi_tts
I (4765) AFE_CONFIG: Set WakeNet Model: wn9_nihaoxiaozhi_tts
MC Quantized wakenet9: wakenet9l_tts1h8_你好小智_3_0.631_0.635, tigger:v4, mode:0, p:0, (Jun 16 2025 16:18:15)
I (4805) AFE: AFE Version: (1MIC_V250121)
I (4805) AFE: Input PCM Config: total 1 channels(1 microphone, 0 playback), sample rate:16000
I (4815) AFE: AFE Pipeline: [input] ->  -> |VAD(WebRTC)| -> |WakeNet(wn9_nihaoxiaozhi_tts,)| -> [output]
I (4825) AfeWakeWord: Audio detection task started, feed size: 512 fetch size: 512
I (4825) Application: STATE: idle
I (4855) SystemInfo: free sram: 104907 minimal sram: 104335
I (4855) Application: Resampling audio from 16000 to 24000
I (4855) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (14445) SystemInfo: free sram: 105775 minimal sram: 104323
I (16855) AudioCodec: Set output enable to false
I (21775) Application: STATE: connecting
I (21785) WS: Connecting to websocket server: ws://*************:8000/xiaozhi/v1/ with version: 1
I (21785) WebSocket: Connecting to ws://*************:8000/xiaozhi/v1/
I (21885) WS: Session ID: 113671f6-2885-488f-bbe6-358c71a99ebb
I (21885) wifi:Set ps type: 0, coexist: 0

W (21885) Application: Server sample rate 16000 does not match device output sample rate 24000, resampling may cause distortion
I (21895) Application: Wake word detected: 你好小智
I (21925) WS: Session ID: 113671f6-2885-488f-bbe6-358c71a99ebb
I (22185) AfeWakeWord: Encode wake word opus 66 packets in 407 ms
I (22185) Application: STATE: listening
I (22275) Application: >> 嘿，你好呀
I (22275) Application: STATE: speaking
I (22275) AudioCodec: Set output enable to true
I (31445) SystemInfo: free sram: 99391 minimal sram: 96143
I (33525) Application: Abort speaking
I (33565) Application: STATE: listening
I (37615) Application: >> 上映了一部加拿大制度的。
I (37625) Application: STATE: speaking
I (47445) SystemInfo: free sram: 99247 minimal sram: 96143
